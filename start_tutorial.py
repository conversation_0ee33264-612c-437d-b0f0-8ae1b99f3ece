#!/usr/bin/env python3
"""
Transformer学习教程启动器
提供交互式菜单来选择不同的学习模块
"""

import os
import sys
import subprocess
from pathlib import Path



class TutorialLauncher:
    """教程启动器"""

    def __init__(self):
        self.tutorial_dir = Path(__file__).parent / "tutorial_scripts"
        self.tutorials = {
            "1": {
                "name": "注意力机制深度解析",
                "script": "step1_attention.py",
                "description": "学习多头注意力的工作原理，包含可视化和逐步计算",
                "duration": "15-20分钟",
                "difficulty": "⭐⭐⭐"
            },
            "2": {
                "name": "编码器结构深入",
                "script": "step2_encoder.py",
                "description": "理解位置编码、编码器层和完整编码器的工作机制",
                "duration": "20-25分钟",
                "difficulty": "⭐⭐⭐⭐"
            },
            "3": {
                "name": "训练过程解析",
                "script": "step3_training.py",
                "description": "深入理解训练过程，包含梯度流、学习率调度等",
                "duration": "25-30分钟",
                "difficulty": "⭐⭐⭐⭐⭐"
            },
            "4": {
                "name": "可视化工具演示",
                "script": "visualize.py",
                "description": "生成各种可视化图表，帮助理解Transformer架构",
                "duration": "10-15分钟",
                "difficulty": "⭐⭐"
            }
        }

        self.quick_actions = {
            "q": {
                "name": "快速训练演示",
                "command": "python quick_train.py",
                "description": "运行快速训练，验证整个流程"
            },
            "t": {
                "name": "模型测试",
                "command": "python test_model.py",
                "description": "运行完整的模型测试套件"
            },
            "d": {
                "name": "数据管理",
                "command": "python manage_data.py info",
                "description": "查看数据目录信息"
            }
        }

    def show_welcome(self):
        """显示欢迎信息"""
        print("🎓" + "=" * 60 + "🎓")
        print("    欢迎来到 Transformer 深度学习教程！")
        print("=" * 64)
        print()
        print("📚 本教程将带你深入理解Transformer的每个细节：")
        print("   • 注意力机制的数学原理和实现")
        print("   • 编码器和解码器的结构分析")
        print("   • 训练过程的详细跟踪")
        print("   • 丰富的可视化和交互式学习")
        print()
        print("💡 学习建议：")
        print("   1. 按顺序学习各个模块")
        print("   2. 仔细观察每个可视化图表")
        print("   3. 理解每个计算步骤")
        print("   4. 做笔记记录关键发现")
        print()
        print("🔧 环境要求：")
        print("   • Python 3.8+")
        print("   • PyTorch")
        print("   • matplotlib, seaborn")
        print("   • 建议使用GPU（可选）")
        print()

    def show_menu(self):
        """显示主菜单"""
        print("📋 请选择学习模块：")
        print()

        # 显示教程模块
        print("🎯 核心教程：")
        for key, tutorial in self.tutorials.items():
            print(f"   [{key}] {tutorial['name']}")
            print(f"       📝 {tutorial['description']}")
            print(f"       ⏱️  预计时间: {tutorial['duration']}")
            print(f"       🌟 难度: {tutorial['difficulty']}")
            print()

        # 显示快速操作
        print("⚡ 快速操作：")
        for key, action in self.quick_actions.items():
            print(f"   [{key}] {action['name']}")
            print(f"       📝 {action['description']}")
            print()

        # 其他选项
        print("🔧 其他选项：")
        print("   [h] 显示帮助信息")
        print("   [s] 系统状态检查")
        print("   [c] 清理缓存文件")
        print("   [x] 退出教程")
        print()

    def run_tutorial(self, script_name):
        """运行指定的教程脚本"""
        script_path = self.tutorial_dir / script_name

        if not script_path.exists():
            print(f"❌ 教程脚本不存在: {script_path}")
            return False

        print(f"🚀 启动教程: {script_name}")
        print("=" * 50)

        try:
            # 运行教程脚本
            result = subprocess.run([
                sys.executable, str(script_path)
            ], cwd=Path(__file__).parent)

            if result.returncode == 0:
                print("\n✅ 教程完成！")
                return True
            else:
                print(f"\n❌ 教程运行出错，返回码: {result.returncode}")
                return False

        except KeyboardInterrupt:
            print("\n⏹️  教程被用户中断")
            return False
        except Exception as e:
            print(f"\n❌ 运行教程时出错: {e}")
            return False

    def run_command(self, command):
        """运行指定命令"""
        print(f"🚀 执行命令: {command}")
        print("=" * 50)

        try:
            result = subprocess.run(
                command.split(),
                cwd=Path(__file__).parent
            )

            if result.returncode == 0:
                print("\n✅ 命令执行完成！")
                return True
            else:
                print(f"\n❌ 命令执行出错，返回码: {result.returncode}")
                return False

        except KeyboardInterrupt:
            print("\n⏹️  命令被用户中断")
            return False
        except Exception as e:
            print(f"\n❌ 执行命令时出错: {e}")
            return False

    def show_help(self):
        """显示帮助信息"""
        print("📖 帮助信息")
        print("=" * 50)
        print()
        print("🎯 学习路径建议：")
        print("   1. 新手：1 → 2 → 4 → q")
        print("   2. 有基础：1 → 2 → 3 → t → q")
        print("   3. 深入研究：全部模块 + 阅读源码")
        print()
        print("🔧 故障排除：")
        print("   • 如果出现导入错误，运行: pip install -r requirements.txt")
        print("   • 如果matplotlib显示问题，检查GUI后端设置")
        print("   • 如果CUDA错误，可以使用CPU模式")
        print()
        print("📁 文件说明：")
        print("   • tutorial_scripts/: 教程脚本目录")
        print("   • src/: 源代码实现")
        print("   • logs/: 训练日志和可视化")
        print("   • data/: 数据集缓存")
        print()
        print("🌐 更多资源：")
        print("   • 原论文: Attention Is All You Need")
        print("   • 可视化解释: The Illustrated Transformer")
        print("   • 代码仓库: 查看 README.md")
        print()

    def check_system_status(self):
        """检查系统状态"""
        print("🔍 系统状态检查")
        print("=" * 50)

        # 检查Python版本
        python_version = sys.version.split()[0]
        print(f"🐍 Python版本: {python_version}")

        # 检查关键包
        packages = ['torch', 'numpy', 'matplotlib', 'seaborn']
        for package in packages:
            try:
                __import__(package)
                print(f"✅ {package}: 已安装")
            except ImportError:
                print(f"❌ {package}: 未安装")

        # 检查GPU
        try:
            import torch
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                print(f"🚀 GPU: {gpu_name}")
            else:
                print("💻 GPU: 不可用，将使用CPU")
        except:
            print("❓ GPU: 无法检测")

        # 检查目录结构
        important_dirs = ['src', 'tutorial_scripts', 'data', 'logs']
        for dir_name in important_dirs:
            dir_path = Path(__file__).parent / dir_name
            if dir_path.exists():
                print(f"📁 {dir_name}/: 存在")
            else:
                print(f"❌ {dir_name}/: 不存在")

        print()

    def clean_cache(self):
        """清理缓存文件"""
        print("🧹 清理缓存文件")
        print("=" * 50)

        cache_patterns = [
            "**/__pycache__",
            "**/*.pyc",
            "tutorial_scripts/*.png",
            "logs/tensorboard/*"
        ]

        import glob
        total_cleaned = 0

        for pattern in cache_patterns:
            files = glob.glob(str(Path(__file__).parent / pattern), recursive=True)
            for file_path in files:
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        total_cleaned += 1
                    elif os.path.isdir(file_path):
                        import shutil
                        shutil.rmtree(file_path)
                        total_cleaned += 1
                except Exception as e:
                    print(f"⚠️  无法删除 {file_path}: {e}")

        print(f"✅ 已清理 {total_cleaned} 个文件/目录")
        print()

    def run(self):
        """运行教程启动器"""
        self.show_welcome()

        while True:
            self.show_menu()

            try:
                choice = input("请选择 (输入数字或字母): ").strip().lower()
                print()

                if choice in self.tutorials:
                    # 运行教程
                    tutorial = self.tutorials[choice]
                    print(f"📚 开始学习: {tutorial['name']}")
                    print(f"📝 {tutorial['description']}")
                    print(f"⏱️  预计时间: {tutorial['duration']}")
                    print()

                    confirm = input("确认开始学习? (y/N): ").strip().lower()
                    if confirm == 'y':
                        self.run_tutorial(tutorial['script'])

                elif choice in self.quick_actions:
                    # 执行快速操作
                    action = self.quick_actions[choice]
                    print(f"⚡ 执行: {action['name']}")
                    print(f"📝 {action['description']}")
                    print()

                    confirm = input("确认执行? (y/N): ").strip().lower()
                    if confirm == 'y':
                        self.run_command(action['command'])

                elif choice == 'h':
                    self.show_help()

                elif choice == 's':
                    self.check_system_status()

                elif choice == 'c':
                    confirm = input("确认清理缓存文件? (y/N): ").strip().lower()
                    if confirm == 'y':
                        self.clean_cache()

                elif choice == 'x':
                    print("👋 感谢使用Transformer学习教程！")
                    print("🎓 继续你的深度学习之旅！")
                    break

                else:
                    print("❌ 无效选择，请重新输入")

                print()
                input("按回车键继续...")
                print("\n" + "=" * 64 + "\n")

            except KeyboardInterrupt:
                print("\n\n👋 你按下了Ctrl+c退出程序，再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")
                print("请重试或联系支持")


def main():
    """主函数"""
    launcher = TutorialLauncher()
    launcher.run()


if __name__ == "__main__":
    from src.utils.utils import setup_proxy
    setup_proxy()
    main()
