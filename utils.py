"""
通用工具函数
"""

import os
import warnings


def setup_proxy():
    """设置代理环境变量"""
    proxy_settings = {
        'https_proxy': 'http://127.0.0.1:7890',
        'http_proxy': 'http://127.0.0.1:7890',
        'all_proxy': 'socks5://127.0.0.1:7890',
        'no_proxy': '127.0.0.1,localhost'
    }
    
    for key, value in proxy_settings.items():
        os.environ[key] = value
        os.environ[key.upper()] = value
    
    print("🌐 代理设置完成")


def setup_chinese_font():
    """
    设置matplotlib中文字体显示

    这个函数会：
    1. 自动检测系统中可用的中文字体
    2. 按优先级设置最佳的中文字体
    3. 抑制matplotlib的字体警告
    4. 确保中文字符正常显示

    支持的字体（按优先级）：
    - SimHei (黑体) - Windows系统
    - Microsoft YaHei (微软雅黑) - Windows系统
    - WenQuanYi Micro Hei (文泉驿微米黑) - Linux系统
    - Noto Sans CJK SC (思源黑体) - 跨平台
    - Source Han Sans CN (Adobe思源黑体) - 跨平台
    - DejaVu Sans (备选英文字体)

    使用方法：
        import matplotlib.pyplot as plt
        from utils import setup_chinese_font

        setup_chinese_font()  # 在绘图前调用
        plt.title('中文标题')  # 现在可以正常显示中文
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm

        # 中文字体优先级列表
        chinese_fonts = [
            'SimHei',                # Windows 黑体
            'Microsoft YaHei',       # Windows 微软雅黑
            'WenQuanYi Micro Hei',   # Linux 文泉驿微米黑
            'Noto Sans CJK SC',      # Google Noto 字体
            'Source Han Sans CN',    # Adobe 思源黑体
            'AR PL UMing CN',        # 文鼎PL细上海宋
            'Droid Sans Fallback',   # Android 备选字体
            'DejaVu Sans'            # 备选英文字体
        ]

        # 获取系统中所有可用字体
        available_fonts = [f.name for f in fm.fontManager.ttflist]

        # 查找第一个可用的中文字体
        selected_font = None
        for font in chinese_fonts:
            if font in available_fonts:
                selected_font = font
                break

        # 设置字体
        if selected_font:
            plt.rcParams['font.sans-serif'] = [selected_font, 'DejaVu Sans']
            print(f"🎨 中文字体设置完成: {selected_font}")
        else:
            # 如果没有找到中文字体，使用默认设置
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            print("⚠️  未找到中文字体，使用默认字体")

        # 设置负号正常显示
        plt.rcParams['axes.unicode_minus'] = False

        # 抑制matplotlib字体警告
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
        warnings.filterwarnings('ignore', message='.*Glyph.*missing from font.*')
        warnings.filterwarnings('ignore', message='.*does not have a glyph.*')

        return selected_font

    except ImportError:
        print("⚠️  matplotlib未安装，跳过中文字体设置")
        return None
    except Exception as e:
        print(f"⚠️  中文字体设置失败: {e}")
        return None


def setup_chinese_font_simple():
    """
    简化版中文字体设置

    直接使用最常见的字体设置，适用于大多数情况
    这是教程脚本中使用的简化版本

    使用方法：
        from utils import setup_chinese_font_simple
        setup_chinese_font_simple()
    """
    try:
        import matplotlib.pyplot as plt

        # 简单直接的字体设置
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 抑制字体警告
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

        print("🎨 中文字体设置完成 (简化版)")
        return True

    except ImportError:
        print("⚠️  matplotlib未安装，跳过中文字体设置")
        return False
    except Exception as e:
        print(f"⚠️  中文字体设置失败: {e}")
        return False


def check_chinese_font_support():
    """
    检查系统中文字体支持情况

    返回：
        dict: 包含字体支持信息的字典
    """
    try:
        import matplotlib.font_manager as fm # 导入字体管理模块

        # 获取所有字体
        all_fonts = [f.name for f in fm.fontManager.ttflist]

        # 常见中文字体
        chinese_fonts = {
            'SimHei': 'Windows 黑体',
            'Microsoft YaHei': 'Windows 微软雅黑',
            'WenQuanYi Micro Hei': 'Linux 文泉驿微米黑',
            'Noto Sans CJK SC': 'Google Noto 字体',
            'Source Han Sans CN': 'Adobe 思源黑体',
            'AR PL UMing CN': '文鼎PL细上海宋',
            'Droid Sans Fallback': 'Android 备选字体'
        }

        # 检查支持情况
        supported_fonts = {}
        for font_name, description in chinese_fonts.items():
            supported_fonts[font_name] = {
                'available': font_name in all_fonts,
                'description': description
            }

        # 统计信息
        total_fonts = len(all_fonts)
        chinese_count = sum(1 for info in supported_fonts.values() if info['available']) # 统计中文字体数量

        result = {
            'total_fonts': total_fonts,
            'chinese_fonts_available': chinese_count,
            'supported_fonts': supported_fonts,
            'all_fonts': sorted(set(all_fonts))
        }

        return result

    except ImportError:
        return {'error': 'matplotlib未安装'}
    except Exception as e:
        return {'error': str(e)}
