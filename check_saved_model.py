#!/usr/bin/env python3
"""
检查保存的模型信息
分析训练完成后的模型数据
"""

import torch
import os
from pathlib import Path
import yaml
from datetime import datetime

def check_model_files():
    """检查模型文件"""
    print("📁 检查模型文件...")
    
    models_dir = Path("./models")
    logs_dir = Path("./logs")
    
    print(f"\n📂 模型目录: {models_dir.absolute()}")
    if models_dir.exists():
        model_files = list(models_dir.glob("*.pt"))
        if model_files:
            for model_file in model_files:
                size_mb = model_file.stat().st_size / (1024 * 1024)
                mtime = datetime.fromtimestamp(model_file.stat().st_mtime)
                print(f"   ✅ {model_file.name}")
                print(f"      大小: {size_mb:.1f} MB")
                print(f"      修改时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("   ❌ 没有找到模型文件")
    else:
        print("   ❌ 模型目录不存在")
    
    print(f"\n📂 日志目录: {logs_dir.absolute()}")
    if logs_dir.exists():
        tensorboard_dir = logs_dir / "tensorboard"
        if tensorboard_dir.exists():
            tb_files = list(tensorboard_dir.glob("events.out.tfevents.*"))
            print(f"   ✅ TensorBoard日志文件: {len(tb_files)} 个")
            for tb_file in tb_files:
                size_kb = tb_file.stat().st_size / 1024
                mtime = datetime.fromtimestamp(tb_file.stat().st_mtime)
                print(f"      - {tb_file.name[:30]}... ({size_kb:.1f} KB, {mtime.strftime('%H:%M:%S')})")
        else:
            print("   ❌ TensorBoard目录不存在")
    else:
        print("   ❌ 日志目录不存在")

def analyze_model_checkpoint(model_path="./models/best_model.pt"):
    """分析模型检查点"""
    print(f"\n🔍 分析模型检查点: {model_path}")
    
    if not os.path.exists(model_path):
        print("   ❌ 模型文件不存在")
        return
    
    try:
        # 加载检查点 (PyTorch 2.6+ 需要设置 weights_only=False)
        checkpoint = torch.load(model_path, map_location='cpu', weights_only=False)
        
        print("   ✅ 模型加载成功")
        print(f"\n📊 检查点信息:")
        
        # 基本信息
        if 'epoch' in checkpoint:
            print(f"   训练轮数: {checkpoint['epoch']}")
        if 'global_step' in checkpoint:
            print(f"   全局步数: {checkpoint['global_step']}")
        if 'best_val_loss' in checkpoint:
            print(f"   最佳验证损失: {checkpoint['best_val_loss']:.4f}")
        
        # 模型状态
        if 'model_state_dict' in checkpoint:
            model_state = checkpoint['model_state_dict']
            print(f"\n🏗️  模型结构信息:")
            
            # 统计参数
            total_params = 0
            layer_info = {}
            
            for name, param in model_state.items():
                layer_name = name.split('.')[0]
                param_count = param.numel()
                total_params += param_count
                
                if layer_name not in layer_info:
                    layer_info[layer_name] = {'count': 0, 'params': 0}
                layer_info[layer_name]['count'] += 1
                layer_info[layer_name]['params'] += param_count
            
            print(f"   总参数数量: {total_params:,}")
            print(f"   层数统计:")
            for layer, info in sorted(layer_info.items()):
                print(f"      {layer}: {info['count']} 层, {info['params']:,} 参数")
        
        # 优化器状态
        if 'optimizer_state_dict' in checkpoint:
            print(f"\n⚙️  优化器信息:")
            opt_state = checkpoint['optimizer_state_dict']
            if 'param_groups' in opt_state:
                for i, group in enumerate(opt_state['param_groups']):
                    print(f"   参数组 {i}:")
                    print(f"      学习率: {group.get('lr', 'N/A')}")
                    print(f"      权重衰减: {group.get('weight_decay', 'N/A')}")
        
        # 训练历史
        if 'train_history' in checkpoint:
            history = checkpoint['train_history']
            print(f"\n📈 训练历史:")
            for key, values in history.items():
                if values:
                    print(f"   {key}: {len(values)} 个记录")
                    if len(values) >= 2:
                        print(f"      初始值: {values[0]:.4f}")
                        print(f"      最终值: {values[-1]:.4f}")
        
        # 配置信息
        if 'config' in checkpoint:
            config = checkpoint['config']
            print(f"\n⚙️  训练配置:")
            if 'model' in config:
                model_config = config['model']
                print(f"   模型维度: {model_config.get('d_model', 'N/A')}")
                print(f"   注意力头数: {model_config.get('n_heads', 'N/A')}")
                print(f"   编码器层数: {model_config.get('n_layers', 'N/A')}")
                print(f"   词汇表大小: {model_config.get('vocab_size', 'N/A')}")
            
            if 'training' in config:
                train_config = config['training']
                print(f"   批次大小: {train_config.get('batch_size', 'N/A')}")
                print(f"   学习率: {train_config.get('learning_rate', 'N/A')}")
                print(f"   训练轮数: {train_config.get('num_epochs', 'N/A')}")
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")

def show_usage_examples():
    """显示模型使用示例"""
    print(f"\n🚀 模型使用示例:")
    print(f"")
    print(f"1. 加载模型进行推理:")
    print(f"   python demo.py --model models/best_model.pt --task translation")
    print(f"")
    print(f"2. 继续训练:")
    print(f"   python train.py --config config.yaml --resume models/best_model.pt")
    print(f"")
    print(f"3. 模型测试:")
    print(f"   python test_model.py --model models/best_model.pt")
    print(f"")
    print(f"4. 查看TensorBoard日志:")
    print(f"   tensorboard --logdir logs/tensorboard")
    print(f"   然后访问: http://localhost:6006")

def main():
    """主函数"""
    print("🔍 检查训练完成后的模型数据")
    print("="*50)
    
    # 检查文件
    check_model_files()
    
    # 分析模型
    analyze_model_checkpoint()
    
    # 显示使用示例
    show_usage_examples()
    
    print(f"\n✅ 检查完成！")
    print(f"你的模型已成功保存在 ./models/best_model.pt")
    print(f"训练日志保存在 ./logs/tensorboard/")

if __name__ == "__main__":
    main()
