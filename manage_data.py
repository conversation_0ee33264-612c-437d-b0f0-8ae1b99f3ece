#!/usr/bin/env python3
"""
数据管理脚本
用于下载、清理和管理训练数据
"""

import argparse
import shutil
import os
from pathlib import Path
from utils import setup_proxy

# 设置代理
setup_proxy()


def get_data_info():
    """获取数据目录信息"""
    project_root = Path(__file__).parent
    data_dir = project_root / "data"
    cache_dir = data_dir / "cache"

    print("📁 数据目录信息:")
    print(f"   项目根目录: {project_root}")
    print(f"   数据目录: {data_dir}")
    print(f"   缓存目录: {cache_dir}")

    if data_dir.exists():
        # 计算目录大小
        total_size = sum(f.stat().st_size for f in data_dir.rglob('*') if f.is_file()) # 计算目录大小
        print(f"   数据目录大小: {total_size / 1024 / 1024:.1f} MB")

        # 列出子目录
        subdirs = [d for d in data_dir.iterdir() if d.is_dir()]
        if subdirs:
            print("   子目录:")
            for subdir in subdirs:
                subdir_size = sum(f.stat().st_size for f in subdir.rglob('*') if f.is_file())
                print(f"     - {subdir.name}: {subdir_size / 1024 / 1024:.1f} MB")
    else:
        print("   数据目录不存在")

    return data_dir, cache_dir


def download_data(dataset_name: str, subset_size: int = None):
    """下载数据集"""
    print(f"📥 下载数据集: {dataset_name}")

    # 确保数据目录存在
    data_dir, cache_dir = get_data_info()
    cache_dir.mkdir(parents=True, exist_ok=True)

    try:
        if dataset_name == "imdb":
            # 下载IMDB数据集
            from src.data.dataset import IMDBDataset

            print("   下载IMDB训练集...")
            train_dataset = IMDBDataset(
                split="train",
                cache_dir=str(cache_dir),
                subset_size=subset_size
            )

            print("   下载IMDB测试集...")
            test_dataset = IMDBDataset(
                split="test",
                cache_dir=str(cache_dir),
                subset_size=subset_size // 4 if subset_size else None
            )

            print(f"✅ IMDB数据集下载完成")
            print(f"   训练样本数: {len(train_dataset)}")
            print(f"   测试样本数: {len(test_dataset)}")

        elif dataset_name == "translation":
            # 下载翻译数据集
            from src.data.dataset import TranslationDataset

            print("   创建翻译数据集...")
            dataset = TranslationDataset(
                cache_dir=str(cache_dir),
                subset_size=subset_size or 1000
            )

            print(f"✅ 翻译数据集准备完成")
            print(f"   样本数: {len(dataset)}")
            print(f"   源词汇表大小: {len(dataset.src_vocab)}")
            print(f"   目标词汇表大小: {len(dataset.tgt_vocab)}")

        else:
            print(f"❌ 不支持的数据集: {dataset_name}")
            return False

        # 显示最终的数据目录信息
        print("\n" + "=" * 30)
        get_data_info()

        return True

    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False


def clean_data(confirm: bool = False):
    """清理数据目录"""
    data_dir, cache_dir = get_data_info()

    if not data_dir.exists():
        print("📁 数据目录不存在，无需清理")
        return

    if not confirm:
        print("⚠️  这将删除所有下载的数据集和缓存文件!")
        response = input("确认删除? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ 取消清理")
            return

    try:
        print("🗑️  清理数据目录...")

        # 删除缓存目录
        if cache_dir.exists():
            shutil.rmtree(cache_dir)
            print(f"   已删除缓存目录: {cache_dir}")

        # 删除其他数据文件
        for item in data_dir.iterdir():
            if item.is_dir() and item.name != "cache":
                shutil.rmtree(item)
                print(f"   已删除目录: {item}")
            elif item.is_file():
                item.unlink()
                print(f"   已删除文件: {item}")

        # 如果数据目录为空，也删除它
        if not any(data_dir.iterdir()):
            data_dir.rmdir()
            print(f"   已删除空的数据目录: {data_dir}")

        print("✅ 数据清理完成")

    except Exception as e:
        print(f"❌ 清理失败: {e}")


def create_data_structure():
    """创建数据目录结构"""
    print("📁 创建数据目录结构...")

    project_root = Path(__file__).parent
    directories = [
        "data",
        "data/cache",
        "data/raw",
        "data/processed",
        "data/models",
        "logs",
        "models"
    ]

    for dir_path in directories:
        full_path = project_root / dir_path
        full_path.mkdir(parents=True, exist_ok=True) # 创建目录,parents=True 表示创建父目录,exists=True 表示如果目录存在则不创建
        print(f"   创建目录: {full_path}")

    # 创建.gitignore文件
    gitignore_path = project_root / "data" / ".gitignore"
    if not gitignore_path.exists():
        with open(gitignore_path, 'w') as f:
            f.write("# 忽略所有数据文件\n")
            f.write("*\n")
            f.write("# 但保留目录结构\n")
            f.write("!.gitignore\n")
            f.write("!README.md\n")
        print(f"   创建.gitignore: {gitignore_path}")

    print("✅ 数据目录结构创建完成")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据管理工具")
    parser.add_argument("action", choices=["info", "download", "clean", "setup"],
                        help="操作类型")
    parser.add_argument("--dataset", type=str, choices=["imdb", "translation"],
                        help="数据集名称")
    parser.add_argument("--subset-size", type=int, help="数据集子集大小")
    parser.add_argument("--confirm", action="store_true", help="确认操作，跳过提示")

    args = parser.parse_args()

    if args.action == "info":
        get_data_info()

    elif args.action == "download":
        if not args.dataset:
            print("❌ 请指定数据集名称 (--dataset)")
            return
        download_data(args.dataset, args.subset_size)

    elif args.action == "clean":
        clean_data(args.confirm)

    elif args.action == "setup":
        create_data_structure()


if __name__ == "__main__":
    main()
