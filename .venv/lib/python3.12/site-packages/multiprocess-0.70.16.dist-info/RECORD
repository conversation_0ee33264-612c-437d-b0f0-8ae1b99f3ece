_multiprocess/__init__.py,sha256=zX5_h36TGSL0brHRtBvCL5E59ccW7yjL79i-Y399ODM,321
_multiprocess/__pycache__/__init__.cpython-312.pyc,,
multiprocess-0.70.16.dist-info/COPYING,sha256=n3_yfLkw0sMgLuB-PS1hRvTeZ20GmjPaMWbJjNuoOpU,1493
multiprocess-0.70.16.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
multiprocess-0.70.16.dist-info/LICENSE,sha256=6XUJedJKg2dhI98BD3PMtVtZvRFT-oGczkOr5B4tEEA,1930
multiprocess-0.70.16.dist-info/METADATA,sha256=Sv2eH2CjjyjVYaryTKqHkbJTlxlVA-SbmziCgkBJeQ0,7151
multiprocess-0.70.16.dist-info/RECORD,,
multiprocess-0.70.16.dist-info/WHEEL,sha256=5fgWN-TjI_Xv7EKUndC5Gdk-CmDdjWHfedmCHAp9Yhg,94
multiprocess-0.70.16.dist-info/top_level.txt,sha256=qtJc8GNdvi6suNpISX0Myln9AXJBYrNuas1MCqRPPqg,27
multiprocess/__info__.py,sha256=84TUBn1oJMNpbVvXKs0lKyfLYaZvRr-ZVh1zHM9VeCY,7997
multiprocess/__init__.py,sha256=XWUBDGorUkDW04h64xe51pUV9N5gzvSDj3tNT2ekifw,1856
multiprocess/__pycache__/__info__.cpython-312.pyc,,
multiprocess/__pycache__/__init__.cpython-312.pyc,,
multiprocess/__pycache__/connection.cpython-312.pyc,,
multiprocess/__pycache__/context.cpython-312.pyc,,
multiprocess/__pycache__/forkserver.cpython-312.pyc,,
multiprocess/__pycache__/heap.cpython-312.pyc,,
multiprocess/__pycache__/managers.cpython-312.pyc,,
multiprocess/__pycache__/pool.cpython-312.pyc,,
multiprocess/__pycache__/popen_fork.cpython-312.pyc,,
multiprocess/__pycache__/popen_forkserver.cpython-312.pyc,,
multiprocess/__pycache__/popen_spawn_posix.cpython-312.pyc,,
multiprocess/__pycache__/popen_spawn_win32.cpython-312.pyc,,
multiprocess/__pycache__/process.cpython-312.pyc,,
multiprocess/__pycache__/queues.cpython-312.pyc,,
multiprocess/__pycache__/reduction.cpython-312.pyc,,
multiprocess/__pycache__/resource_sharer.cpython-312.pyc,,
multiprocess/__pycache__/resource_tracker.cpython-312.pyc,,
multiprocess/__pycache__/shared_memory.cpython-312.pyc,,
multiprocess/__pycache__/sharedctypes.cpython-312.pyc,,
multiprocess/__pycache__/spawn.cpython-312.pyc,,
multiprocess/__pycache__/synchronize.cpython-312.pyc,,
multiprocess/__pycache__/util.cpython-312.pyc,,
multiprocess/connection.py,sha256=-MnrMNG2rA4O-2U5s0Ct_nXi1Q8x1QFTAShwSELFiJ0,41577
multiprocess/context.py,sha256=xqunfqOp2vRQqrw-eFq3q4jfwa3GdTrL4Mh1M4AvtgI,11686
multiprocess/dummy/__init__.py,sha256=kSekDqD_NCy0FDg7XnxZSgW-Ldg1_iRr07sNwDajKpA,3061
multiprocess/dummy/__pycache__/__init__.cpython-312.pyc,,
multiprocess/dummy/__pycache__/connection.cpython-312.pyc,,
multiprocess/dummy/connection.py,sha256=1j3Rl5_enBM-_kMO6HDmum3kPAoFE4Zs485HV5H-V6s,1598
multiprocess/forkserver.py,sha256=_WT6-elIjrr7t8RXoZ6u0RGHgLaqoWweAiqwaWDlUCw,12130
multiprocess/heap.py,sha256=9rt5u5m5rkhJNfDWiCLpYDoWIt0LbElmx52yMqk7phQ,11626
multiprocess/managers.py,sha256=wzD4jg66U41kwWsqguoDZN0z-JKFbrn0loK4b_fU3GA,47675
multiprocess/pool.py,sha256=QYzUHqXDQeZsaXmjgHD29VLLvfBTkHMCK20Vxn5T9IA,32760
multiprocess/popen_fork.py,sha256=Nvq5vVId24UfkOQxXhxZbcXuo8d6YMc409yRXAamTd0,2374
multiprocess/popen_forkserver.py,sha256=SrEbV8Wv0Uu_UegkaW-cayXRdjTGXr560Yyy90pj-yE,2227
multiprocess/popen_spawn_posix.py,sha256=l7XSWqR5UWiUSJh35qeSElLuNfUeEYwvH5HzKRnnyqg,2029
multiprocess/popen_spawn_win32.py,sha256=XA9nNjov3-JjmO5ztPE3Ba9zJbR-W4UunYPjm0Y8C98,4353
multiprocess/process.py,sha256=WD3nlOajRpvtXuw18J4r7fUKGumP-h7oV21bqhQihEk,12133
multiprocess/queues.py,sha256=IyJsaDl3X6RJD8yOuYX6iGU9OXwi4Um6BA3tNhQt1OI,12615
multiprocess/reduction.py,sha256=NQQ6KbDhmuAyaDeWaIarTZQokGPhcFda1poNnPm5uNc,9637
multiprocess/resource_sharer.py,sha256=nEApLhMQqd8KunfaNKl3n8vdeiCGPxKrSL1Ja0nNAEk,5132
multiprocess/resource_tracker.py,sha256=YX78ClEGXDk1ieCaKRlJ5K1pNZnsvTyz6bBuWmGqZFw,10449
multiprocess/shared_memory.py,sha256=UTAecHECIOHElP9Tg6yURCo4pKZiLy65TkASjEXeGus,18458
multiprocess/sharedctypes.py,sha256=d-9SKRJHRlJJC331IxEoWOUXIeY9zxCbhWejXOmzGw0,6306
multiprocess/spawn.py,sha256=GPe8Ht5UI4wqLSGcPJvo8rcJmDcEK2A_bVNUEyG6ozI,9641
multiprocess/synchronize.py,sha256=xMTFX2wwOgbg_bXYgaL49zZAocuraXQ0XG3Uh8Uu7F0,12506
multiprocess/tests/__init__.py,sha256=AX00HJqMOEPdte08EFBG_5cpDjscm5XZQpN3bbClAsA,209750
multiprocess/tests/__main__.py,sha256=kePVxic_T6xt2jmqlQFlR4ef0ZAGaD4S-od3pXL-pnQ,888
multiprocess/tests/__pycache__/__init__.cpython-312.pyc,,
multiprocess/tests/__pycache__/__main__.cpython-312.pyc,,
multiprocess/tests/__pycache__/mp_fork_bomb.cpython-312.pyc,,
multiprocess/tests/__pycache__/mp_preload.cpython-312.pyc,,
multiprocess/tests/__pycache__/test_multiprocessing_main_handling.cpython-312.pyc,,
multiprocess/tests/mp_fork_bomb.py,sha256=6ADOEzh1aXHZ21aOGoBPhKcgB5sj15G9tQVgSc6GrlY,448
multiprocess/tests/mp_preload.py,sha256=1-WvLFMaPoH-vZbpUaJvvZHFxTpA9tgmct2vblQy99M,365
multiprocess/tests/test_multiprocessing_fork/__init__.py,sha256=h4YpM8po-3m2kFeoCuV9ZsKDP8UgY-e1Sx3mat9oI9o,829
multiprocess/tests/test_multiprocessing_fork/__pycache__/__init__.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_fork/__pycache__/test_manager.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_fork/__pycache__/test_misc.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_fork/__pycache__/test_processes.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_fork/__pycache__/test_threads.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_fork/test_manager.py,sha256=A5K0x43Vrfe2jAVqFkBZpyzfuFoYm_Ickby4z31-xPg,194
multiprocess/tests/test_multiprocessing_fork/test_misc.py,sha256=KhsBZTAEng6Dn-KAww-_eFMnnf14lWCXayQ4cMjx9Mk,193
multiprocess/tests/test_multiprocessing_fork/test_processes.py,sha256=3bPddzlEnRMkan2cSLfDx77hEydV51Mx-l51ERtwt4Q,196
multiprocess/tests/test_multiprocessing_fork/test_threads.py,sha256=_qq2fWmU5FuGtsOSoesXRV3DyCn0ptEezKrsnsE6MqI,194
multiprocess/tests/test_multiprocessing_forkserver/__init__.py,sha256=GnMnSAaJ_y557T6vZsqu7C1Z_H1gOjGbXqklkfNz6Wc,738
multiprocess/tests/test_multiprocessing_forkserver/__pycache__/__init__.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_forkserver/__pycache__/test_manager.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_forkserver/__pycache__/test_misc.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_forkserver/__pycache__/test_processes.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_forkserver/__pycache__/test_threads.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_forkserver/test_manager.py,sha256=PQTSGlK3U5B2RVaHbW8MJ2pMWeYJj4arKVLm49Fc9TY,200
multiprocess/tests/test_multiprocessing_forkserver/test_misc.py,sha256=EbDnh3_2MTNI3fFuC2GjM4pUUJ9cxRWXEwZZBV9FyoQ,199
multiprocess/tests/test_multiprocessing_forkserver/test_processes.py,sha256=ks2hMY1_y0ZiyEGqgAK340b2M6oupXksCo3QFCsgTVk,202
multiprocess/tests/test_multiprocessing_forkserver/test_threads.py,sha256=H1OyjLOUPkFsnw6QEEp7aSFPLPVndsZX92MAaU_gBFs,200
multiprocess/tests/test_multiprocessing_main_handling.py,sha256=gFVw9bMOg8pFZp0QGPIPlXDtrY8K5poeb54PrTGT0Ow,11847
multiprocess/tests/test_multiprocessing_spawn/__init__.py,sha256=pkhcDlpFcKmrN7-ekKF8Zz69x_X24JWqdIdiDypwJiY,639
multiprocess/tests/test_multiprocessing_spawn/__pycache__/__init__.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_spawn/__pycache__/test_manager.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_spawn/__pycache__/test_misc.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_spawn/__pycache__/test_processes.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_spawn/__pycache__/test_threads.cpython-312.pyc,,
multiprocess/tests/test_multiprocessing_spawn/test_manager.py,sha256=3zMuPfUzqSaRZbNbVpQoU9OU52KaJotd7ps0H-_hdFU,195
multiprocess/tests/test_multiprocessing_spawn/test_misc.py,sha256=Lb8nqsQL893F8TsvjBEh5s7nV37qAmetcLGk5tuZ3Kc,194
multiprocess/tests/test_multiprocessing_spawn/test_processes.py,sha256=HoZZlby6gRZONFoV5-wEC50lQFEawvwT_Jh6yZ4SCag,197
multiprocess/tests/test_multiprocessing_spawn/test_threads.py,sha256=pr9-K0KR8THDzYfGw4PGJ45RcGqREcTNX0OAE14exEw,195
multiprocess/util.py,sha256=zUGS2s1PyQSS90vLctLRC7QzegW56br_JDkLR7pDAFQ,14060
