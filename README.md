# 手动实现的Transformer模型

这是一个从零开始手动实现的Transformer模型，包含完整的训练和推理流程。

## 🌟 特性

- **完整实现**: 从基础组件到完整模型的全面实现
- **详细注释**: 每个模块都有详细的中文注释和说明
- **内存优化**: 针对12GB显存进行优化，支持混合精度训练
- **多任务支持**: 支持文本分类和序列到序列翻译任务
- **可视化**: 集成TensorBoard进行训练监控
- **测试完备**: 包含全面的单元测试和集成测试

## 📁 项目结构

```
myTransfromer/
├── src/                          # 源代码目录
│   ├── transformer/              # Transformer核心模块
│   │   ├── attention.py          # 多头注意力机制
│   │   ├── positional_encoding.py # 位置编码
│   │   ├── feed_forward.py       # 前馈神经网络
│   │   ├── encoder_layer.py      # 编码器层
│   │   ├── encoder.py            # 完整编码器
│   │   ├── decoder_layer.py      # 解码器层
│   │   └── transformer.py       # 完整Transformer模型
│   ├── data/                     # 数据处理模块
│   │   └── dataset.py            # 数据集加载和预处理
│   └── training/                 # 训练模块
│       └── trainer.py            # 训练器
├── config.yaml                   # 模型配置文件
├── train.py                      # 主训练脚本
├── test_model.py                 # 模型测试脚本
├── demo.py                       # 演示脚本
├── quick_train.py                # 快速训练脚本
├── test_environment.py           # 环境测试脚本
└── requirements.txt              # 依赖包列表
```

## 🚀 快速开始

### 1. 环境设置

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt
```

### 2. 环境测试

```bash
python test_environment.py
```

### 3. 模型测试

```bash
python test_model.py
```

### 4. 快速训练

```bash
python quick_train.py
```
