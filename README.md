# 手动实现 Transformer 模型

这个项目从零开始实现一个完整的 Transformer 模型，包含详细的注释和说明。

## 项目结构

```
myTransformer/
├── src/                    # 源代码目录
│   ├── models/            # 模型实现
│   │   ├── __init__.py
│   │   ├── attention.py   # 注意力机制
│   │   ├── encoder.py     # 编码器
│   │   ├── decoder.py     # 解码器
│   │   ├── transformer.py # 完整模型
│   │   └── components.py  # 基础组件
│   ├── data/              # 数据处理
│   │   ├── __init__.py
│   │   └── dataset.py     # 数据集处理
│   ├── training/          # 训练相关
│   │   ├── __init__.py
│   │   ├── trainer.py     # 训练器
│   │   └── utils.py       # 训练工具
│   └── utils/             # 通用工具
│       ├── __init__.py
│       └── helpers.py     # 辅助函数
├── examples/              # 示例和演示
├── tests/                 # 测试文件
├── configs/               # 配置文件
├── requirements.txt       # 依赖包
└── README.md             # 项目说明
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 特性

- 完整的 Transformer 架构实现
- 详细的代码注释和文档
- 适合 12GB 显存的小型模型配置
- 支持多种任务（文本分类、序列到序列等）
- 包含训练和推理示例

## 使用方法

详细的使用方法将在实现完成后更新。
