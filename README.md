# 手动实现的Transformer模型

这是一个从零开始手动实现的Transformer模型，包含完整的训练和推理流程。

## 🌟 特性

- **完整实现**: 从基础组件到完整模型的全面实现
- **详细注释**: 每个模块都有详细的中文注释和说明
- **内存优化**: 针对12GB显存进行优化，支持混合精度训练
- **多任务支持**: 支持文本分类和序列到序列翻译任务
- **可视化**: 集成TensorBoard进行训练监控
- **测试完备**: 包含全面的单元测试和集成测试

## 📁 项目结构

```
myTransfromer/
├── src/                          # 源代码目录
│   ├── transformer/              # Transformer核心模块
│   │   ├── attention.py          # 多头注意力机制
│   │   ├── positional_encoding.py # 位置编码
│   │   ├── feed_forward.py       # 前馈神经网络
│   │   ├── encoder_layer.py      # 编码器层
│   │   ├── encoder.py            # 完整编码器
│   │   ├── decoder_layer.py      # 解码器层
│   │   └── transformer.py       # 完整Transformer模型
│   ├── data/                     # 数据处理模块
│   │   └── dataset.py            # 数据集加载和预处理
│   └── training/                 # 训练模块
│       └── trainer.py            # 训练器
├── config.yaml                   # 模型配置文件
├── train.py                      # 主训练脚本
├── test_model.py                 # 模型测试脚本
├── demo.py                       # 演示脚本
├── quick_train.py                # 快速训练脚本
├── test_environment.py           # 环境测试脚本
└── requirements.txt              # 依赖包列表
```

## 🚀 快速开始

### 1. 环境设置

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
source .venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt
```

### 2. 环境测试

```bash
python test_environment.py
```

### 3. 模型测试

```bash
python test_model.py
```

### 4. 运行单元测试

```bash
python run_tests.py
```

### 5. 数据管理

```bash
# 创建数据目录结构
python manage_data.py setup

# 查看数据信息
python manage_data.py info

# 下载数据集
python manage_data.py download --dataset imdb --subset-size 1000
```

### 6. 快速训练

```bash
python quick_train.py
```

### 7. 完整训练

```bash
# 翻译任务
python train.py --config config.yaml --task translation --subset_size 1000

# 分类任务
python train.py --config config.yaml --task classification --subset_size 1000
```

### 8. 模型演示

```bash
# 交互式演示
python demo.py --config config.yaml --model models/best_model.pt --task translation

# 批量演示
python demo.py --config config.yaml --model models/best_model.pt --task translation --mode batch
```

## 🧪 测试系统

### 单元测试

项目包含完整的单元测试套件：

```bash
# 运行所有测试
python run_tests.py

# 运行特定测试
python run_tests.py test_attention
python run_tests.py test_transformer
```

### 测试覆盖

- ✅ 多头注意力机制测试
- ✅ 位置编码测试
- ✅ 编码器测试
- ✅ 完整Transformer测试
- ✅ 梯度流测试
- ✅ 内存使用测试
- ✅ 性能测试

## 📊 数据管理

### 数据目录结构

```
data/
├── cache/          # HuggingFace缓存
├── raw/            # 原始数据
├── processed/      # 处理后数据
└── models/         # 模型文件
```

### 数据管理命令

```bash
# 创建数据目录
python manage_data.py setup

# 查看数据信息
python manage_data.py info

# 下载IMDB数据集
python manage_data.py download --dataset imdb --subset-size 1000

# 下载翻译数据集
python manage_data.py download --dataset translation --subset-size 500

# 清理数据
python manage_data.py clean
```

### 数据存储位置

- **缓存目录**: `./data/cache/` - 所有HuggingFace数据集和模型缓存
- **模型目录**: `./models/` - 训练好的模型检查点
- **日志目录**: `./logs/` - 训练日志和TensorBoard文件
