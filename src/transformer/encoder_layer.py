"""
Transformer编码器层模块
组合多头注意力、前馈网络、残差连接和层归一化

编码器层的结构：
1. 多头自注意力 + 残差连接 + 层归一化
2. 前馈网络 + 残差连接 + 层归一化

残差连接的作用：
- 缓解梯度消失问题
- 使深层网络更容易训练
- 保持信息流动

层归一化的作用：
- 稳定训练过程
- 加速收敛
- 减少内部协变量偏移
"""

import torch
import torch.nn as nn
from typing import Optional, Tuple

# 导入我们之前实现的模块
from attention import MultiHeadAttention
from feed_forward import PositionwiseFeedForward


class TransformerEncoderLayer(nn.Module):
    """
    Transformer编码器层
    
    结构：
    输入 -> 多头自注意力 -> 残差连接 -> 层归一化 -> 前馈网络 -> 残差连接 -> 层归一化 -> 输出
    """
    
    def __init__(
        self,
        d_model: int,
        n_heads: int,
        d_ff: int,
        dropout: float = 0.1,
        activation: str = "relu",
        norm_first: bool = False
    ):
        """
        初始化编码器层
        
        Args:
            d_model: 模型维度
            n_heads: 注意力头数
            d_ff: 前馈网络隐藏层维度
            dropout: dropout概率
            activation: 前馈网络激活函数
            norm_first: 是否使用Pre-LN结构（层归一化在子层之前）
        """
        super(TransformerEncoderLayer, self).__init__()
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_ff = d_ff
        self.norm_first = norm_first
        
        # 多头自注意力
        self.self_attention = MultiHeadAttention(d_model, n_heads, dropout)
        
        # 前馈网络
        self.feed_forward = PositionwiseFeedForward(d_model, d_ff, dropout, activation)
        
        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
    
    def forward(
        self, 
        x: torch.Tensor, 
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_length, d_model]
            mask: 注意力掩码 [batch_size, seq_length, seq_length]
            return_attention: 是否返回注意力权重
            
        Returns:
            output: 输出张量 [batch_size, seq_length, d_model]
            attention_weights: 注意力权重 (可选)
        """
        if self.norm_first:
            # Pre-LN结构：LayerNorm -> SubLayer -> Residual
            return self._forward_pre_norm(x, mask, return_attention)
        else:
            # Post-LN结构：SubLayer -> Residual -> LayerNorm
            return self._forward_post_norm(x, mask, return_attention)
    
    def _forward_post_norm(
        self, 
        x: torch.Tensor, 
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """Post-LN结构的前向传播"""
        # 1. 多头自注意力子层
        attn_output, attention_weights = self.self_attention(
            x, x, x, mask=mask, return_attention=return_attention
        )
        
        # 残差连接 + 层归一化
        x = self.norm1(x + self.dropout(attn_output))
        
        # 2. 前馈网络子层
        ff_output = self.feed_forward(x)
        
        # 残差连接 + 层归一化
        output = self.norm2(x + self.dropout(ff_output))
        
        return output, attention_weights
    
    def _forward_pre_norm(
        self, 
        x: torch.Tensor, 
        mask: Optional[torch.Tensor] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """Pre-LN结构的前向传播"""
        # 1. 多头自注意力子层
        # 层归一化 -> 自注意力 -> 残差连接
        norm_x = self.norm1(x)
        attn_output, attention_weights = self.self_attention(
            norm_x, norm_x, norm_x, mask=mask, return_attention=return_attention
        )
        x = x + self.dropout(attn_output)
        
        # 2. 前馈网络子层
        # 层归一化 -> 前馈网络 -> 残差连接
        norm_x = self.norm2(x)
        ff_output = self.feed_forward(norm_x)
        output = x + self.dropout(ff_output)
        
        return output, attention_weights


class TransformerEncoderLayerWithCrossAttention(nn.Module):
    """
    带交叉注意力的编码器层
    
    用于处理多模态输入或编码器-解码器结构
    """
    
    def __init__(
        self,
        d_model: int,
        n_heads: int,
        d_ff: int,
        dropout: float = 0.1,
        activation: str = "relu"
    ):
        """
        初始化带交叉注意力的编码器层
        
        Args:
            d_model: 模型维度
            n_heads: 注意力头数
            d_ff: 前馈网络隐藏层维度
            dropout: dropout概率
            activation: 激活函数
        """
        super(TransformerEncoderLayerWithCrossAttention, self).__init__()
        
        # 自注意力
        self.self_attention = MultiHeadAttention(d_model, n_heads, dropout)
        
        # 交叉注意力
        self.cross_attention = MultiHeadAttention(d_model, n_heads, dropout)
        
        # 前馈网络
        self.feed_forward = PositionwiseFeedForward(d_model, d_ff, dropout, activation)
        
        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
    
    def forward(
        self,
        x: torch.Tensor,
        memory: torch.Tensor,
        self_mask: Optional[torch.Tensor] = None,
        cross_mask: Optional[torch.Tensor] = None,
        return_attention: bool = False
    ) -> Tuple[torch.Tensor, Optional[torch.Tensor], Optional[torch.Tensor]]:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_length, d_model]
            memory: 记忆张量（来自其他模态或编码器）[batch_size, memory_length, d_model]
            self_mask: 自注意力掩码
            cross_mask: 交叉注意力掩码
            return_attention: 是否返回注意力权重
            
        Returns:
            output: 输出张量
            self_attention_weights: 自注意力权重 (可选)
            cross_attention_weights: 交叉注意力权重 (可选)
        """
        # 1. 自注意力子层
        self_attn_output, self_attention_weights = self.self_attention(
            x, x, x, mask=self_mask, return_attention=return_attention
        )
        x = self.norm1(x + self.dropout(self_attn_output))
        
        # 2. 交叉注意力子层
        cross_attn_output, cross_attention_weights = self.cross_attention(
            x, memory, memory, mask=cross_mask, return_attention=return_attention
        )
        x = self.norm2(x + self.dropout(cross_attn_output))
        
        # 3. 前馈网络子层
        ff_output = self.feed_forward(x)
        output = self.norm3(x + self.dropout(ff_output))
        
        return output, self_attention_weights, cross_attention_weights


if __name__ == "__main__":
    # 测试编码器层
    print("🧪 测试Transformer编码器层...")
    
    # 参数设置
    batch_size = 2
    seq_length = 10
    d_model = 512
    n_heads = 8
    d_ff = 2048
    
    # 创建测试输入
    x = torch.randn(batch_size, seq_length, d_model)
    print(f"输入形状: {x.shape}")
    
    # 测试标准编码器层 (Post-LN)
    print(f"\n🔄 测试Post-LN编码器层:")
    encoder_layer = TransformerEncoderLayer(d_model, n_heads, d_ff, norm_first=False)
    output, attention_weights = encoder_layer(x, return_attention=True)
    print(f"输出形状: {output.shape}")
    print(f"注意力权重形状: {attention_weights.shape}")
    print(f"参数数量: {sum(p.numel() for p in encoder_layer.parameters()):,}")
    
    # 测试Pre-LN编码器层
    print(f"\n🔄 测试Pre-LN编码器层:")
    encoder_layer_pre = TransformerEncoderLayer(d_model, n_heads, d_ff, norm_first=True)
    output_pre, _ = encoder_layer_pre(x)
    print(f"Pre-LN输出形状: {output_pre.shape}")
    
    # 测试掩码
    print(f"\n🎭 测试掩码:")
    mask = torch.ones(batch_size, seq_length, seq_length)
    mask[:, :, 5:] = 0  # 掩盖后半部分
    
    masked_output, masked_attention = encoder_layer(x, mask=mask, return_attention=True)
    print(f"掩码输出形状: {masked_output.shape}")
    
    # 测试带交叉注意力的编码器层
    print(f"\n🔄 测试交叉注意力编码器层:")
    memory = torch.randn(batch_size, 8, d_model)  # 不同长度的记忆
    cross_encoder = TransformerEncoderLayerWithCrossAttention(d_model, n_heads, d_ff)
    
    cross_output, self_attn, cross_attn = cross_encoder(
        x, memory, return_attention=True
    )
    print(f"交叉注意力输出形状: {cross_output.shape}")
    print(f"自注意力权重形状: {self_attn.shape}")
    print(f"交叉注意力权重形状: {cross_attn.shape}")
    
    # 输出统计
    print(f"\n📊 输出统计:")
    print(f"Post-LN输出均值: {output.mean().item():.6f}, 标准差: {output.std().item():.6f}")
    print(f"Pre-LN输出均值: {output_pre.mean().item():.6f}, 标准差: {output_pre.std().item():.6f}")
    print(f"交叉注意力输出均值: {cross_output.mean().item():.6f}, 标准差: {cross_output.std().item():.6f}")
    
    print("✅ 编码器层测试完成!")
