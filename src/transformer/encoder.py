"""
Transformer编码器模块
堆叠多个编码器层，添加输入嵌入和位置编码

编码器的完整结构：
1. 输入嵌入 (Token Embedding)
2. 位置编码 (Positional Encoding)
3. 多个编码器层堆叠
4. 可选的最终层归一化

编码器的作用：
- 将输入序列编码为上下文相关的表示
- 每一层都能捕获不同层次的特征
- 为下游任务提供丰富的特征表示
"""

import torch
import torch.nn as nn
import math
from typing import Optional, List, Tuple

# 导入我们之前实现的模块
from encoder_layer import TransformerEncoderLayer
from positional_encoding import PositionalEncoding


class TransformerEncoder(nn.Module):
    """
    完整的Transformer编码器
    
    包含：
    - 输入嵌入层
    - 位置编码
    - 多个编码器层
    - 可选的最终层归一化
    """
    
    def __init__(
        self,
        vocab_size: int,
        d_model: int,
        n_heads: int,
        n_layers: int,
        d_ff: int,
        max_length: int = 5000,
        dropout: float = 0.1,
        activation: str = "relu",
        norm_first: bool = False,
        final_norm: bool = True,
        pad_token_id: int = 0
    ):
        """
        初始化Transformer编码器
        
        Args:
            vocab_size: 词汇表大小
            d_model: 模型维度
            n_heads: 注意力头数
            n_layers: 编码器层数
            d_ff: 前馈网络隐藏层维度
            max_length: 最大序列长度
            dropout: dropout概率
            activation: 激活函数
            norm_first: 是否使用Pre-LN结构
            final_norm: 是否在最后添加层归一化
            pad_token_id: 填充token的ID
        """
        super(TransformerEncoder, self).__init__()
        
        self.vocab_size = vocab_size
        self.d_model = d_model
        self.n_heads = n_heads
        self.n_layers = n_layers
        self.pad_token_id = pad_token_id
        
        # 输入嵌入层
        self.token_embedding = nn.Embedding(vocab_size, d_model, padding_idx=pad_token_id)
        
        # 位置编码
        self.positional_encoding = PositionalEncoding(d_model, max_length, dropout)
        
        # 编码器层堆叠
        self.layers = nn.ModuleList([
            TransformerEncoderLayer(
                d_model=d_model,
                n_heads=n_heads,
                d_ff=d_ff,
                dropout=dropout,
                activation=activation,
                norm_first=norm_first
            )
            for _ in range(n_layers)
        ])
        
        # 可选的最终层归一化
        self.final_norm = nn.LayerNorm(d_model) if final_norm else None
        
        # 嵌入缩放因子（按照原论文）
        self.embed_scale = math.sqrt(d_model)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        # 初始化token嵌入
        nn.init.normal_(self.token_embedding.weight, mean=0, std=0.02)
        if self.pad_token_id is not None:
            # 将padding token的嵌入设为0
            nn.init.constant_(self.token_embedding.weight[self.pad_token_id], 0)
    
    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        return_all_hidden_states: bool = False,
        return_attention_weights: bool = False
    ) -> Tuple[torch.Tensor, Optional[List[torch.Tensor]], Optional[List[torch.Tensor]]]:
        """
        前向传播
        
        Args:
            input_ids: 输入token IDs [batch_size, seq_length]
            attention_mask: 注意力掩码 [batch_size, seq_length]
            return_all_hidden_states: 是否返回所有层的隐藏状态
            return_attention_weights: 是否返回所有层的注意力权重
            
        Returns:
            last_hidden_state: 最后一层的隐藏状态 [batch_size, seq_length, d_model]
            all_hidden_states: 所有层的隐藏状态列表 (可选)
            all_attention_weights: 所有层的注意力权重列表 (可选)
        """
        batch_size, seq_length = input_ids.size()
        
        # 1. Token嵌入
        # [batch_size, seq_length] -> [batch_size, seq_length, d_model]
        embeddings = self.token_embedding(input_ids) * self.embed_scale
        
        # 2. 位置编码
        x = self.positional_encoding(embeddings)
        
        # 3. 创建注意力掩码
        if attention_mask is not None:
            # 将attention_mask转换为适合注意力计算的形式
            # [batch_size, seq_length] -> [batch_size, seq_length, seq_length]
            extended_attention_mask = self._create_extended_attention_mask(attention_mask)
        else:
            extended_attention_mask = None
        
        # 存储所有层的输出
        all_hidden_states = [x] if return_all_hidden_states else None
        all_attention_weights = [] if return_attention_weights else None
        
        # 4. 通过所有编码器层
        for i, layer in enumerate(self.layers):
            x, attention_weights = layer(
                x, 
                mask=extended_attention_mask,
                return_attention=return_attention_weights
            )
            
            if return_all_hidden_states:
                all_hidden_states.append(x)
            
            if return_attention_weights:
                all_attention_weights.append(attention_weights)
        
        # 5. 最终层归一化
        if self.final_norm is not None:
            x = self.final_norm(x)
        
        return x, all_hidden_states, all_attention_weights
    
    def _create_extended_attention_mask(self, attention_mask: torch.Tensor) -> torch.Tensor:
        """
        创建扩展的注意力掩码

        Args:
            attention_mask: [batch_size, seq_length] (1表示有效，0表示padding)

        Returns:
            extended_mask: [batch_size, seq_length, seq_length]
        """
        batch_size, seq_length = attention_mask.size()

        # 确保attention_mask是整数类型
        attention_mask = attention_mask.long()

        # 扩展为 [batch_size, seq_length, seq_length]
        extended_mask = attention_mask.unsqueeze(1).expand(batch_size, seq_length, seq_length)

        # 确保只有当query和key都有效时，注意力才有效
        extended_mask = extended_mask & attention_mask.unsqueeze(2)

        return extended_mask
    
    def get_input_embeddings(self) -> nn.Embedding:
        """获取输入嵌入层"""
        return self.token_embedding
    
    def set_input_embeddings(self, new_embeddings: nn.Embedding):
        """设置输入嵌入层"""
        self.token_embedding = new_embeddings
    
    def get_attention_mask_from_input_ids(self, input_ids: torch.Tensor) -> torch.Tensor:
        """
        从input_ids创建注意力掩码
        
        Args:
            input_ids: [batch_size, seq_length]
            
        Returns:
            attention_mask: [batch_size, seq_length]
        """
        return (input_ids != self.pad_token_id).long()


class PooledTransformerEncoder(TransformerEncoder):
    """
    带池化的Transformer编码器
    
    在标准编码器基础上添加池化层，用于分类等任务
    """
    
    def __init__(self, pooling_strategy: str = "cls", **kwargs):
        """
        初始化带池化的编码器
        
        Args:
            pooling_strategy: 池化策略 ("cls", "mean", "max", "first", "last")
            **kwargs: 传递给父类的参数
        """
        super(PooledTransformerEncoder, self).__init__(**kwargs)
        
        self.pooling_strategy = pooling_strategy
        
        # 如果使用CLS策略，需要添加CLS token
        if pooling_strategy == "cls":
            # 扩展词汇表以包含CLS token
            self.cls_token_id = self.vocab_size
            self.token_embedding = nn.Embedding(
                self.vocab_size + 1, 
                self.d_model, 
                padding_idx=self.pad_token_id
            )
            self._init_weights()
    
    def forward(
        self,
        input_ids: torch.Tensor,
        attention_mask: Optional[torch.Tensor] = None,
        **kwargs
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Returns:
            sequence_output: 序列输出 [batch_size, seq_length, d_model]
            pooled_output: 池化输出 [batch_size, d_model]
        """
        # 如果使用CLS策略，在序列开头添加CLS token
        if self.pooling_strategy == "cls":
            input_ids, attention_mask = self._add_cls_token(input_ids, attention_mask)
        
        # 获取编码器输出
        sequence_output, _, _ = super().forward(input_ids, attention_mask, **kwargs)
        
        # 池化
        pooled_output = self._pool_sequence(sequence_output, attention_mask)
        
        return sequence_output, pooled_output
    
    def _add_cls_token(
        self, 
        input_ids: torch.Tensor, 
        attention_mask: Optional[torch.Tensor]
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """添加CLS token"""
        batch_size = input_ids.size(0)
        device = input_ids.device
        
        # 创建CLS token
        cls_tokens = torch.full((batch_size, 1), self.cls_token_id, device=device)
        
        # 拼接CLS token
        input_ids = torch.cat([cls_tokens, input_ids], dim=1)
        
        if attention_mask is not None:
            cls_mask = torch.ones((batch_size, 1), device=device)
            attention_mask = torch.cat([cls_mask, attention_mask], dim=1)
        
        return input_ids, attention_mask
    
    def _pool_sequence(
        self, 
        sequence_output: torch.Tensor, 
        attention_mask: Optional[torch.Tensor]
    ) -> torch.Tensor:
        """池化序列输出"""
        if self.pooling_strategy == "cls":
            # 使用CLS token的输出
            return sequence_output[:, 0]
        
        elif self.pooling_strategy == "mean":
            # 平均池化（忽略padding）
            if attention_mask is not None:
                mask = attention_mask.unsqueeze(-1).float()
                masked_output = sequence_output * mask
                return masked_output.sum(dim=1) / mask.sum(dim=1)
            else:
                return sequence_output.mean(dim=1)
        
        elif self.pooling_strategy == "max":
            # 最大池化
            if attention_mask is not None:
                mask = attention_mask.unsqueeze(-1).float()
                masked_output = sequence_output * mask + (1 - mask) * (-1e9)
                return masked_output.max(dim=1)[0]
            else:
                return sequence_output.max(dim=1)[0]
        
        elif self.pooling_strategy == "first":
            # 使用第一个token
            return sequence_output[:, 0]
        
        elif self.pooling_strategy == "last":
            # 使用最后一个有效token
            if attention_mask is not None:
                lengths = attention_mask.sum(dim=1) - 1  # -1因为索引从0开始
                batch_indices = torch.arange(sequence_output.size(0), device=sequence_output.device)
                return sequence_output[batch_indices, lengths]
            else:
                return sequence_output[:, -1]
        
        else:
            raise ValueError(f"不支持的池化策略: {self.pooling_strategy}")


if __name__ == "__main__":
    # 测试Transformer编码器
    print("🧪 测试Transformer编码器...")
    
    # 参数设置
    vocab_size = 1000
    batch_size = 2
    seq_length = 10
    d_model = 512
    n_heads = 8
    n_layers = 6
    d_ff = 2048
    
    # 创建测试输入
    input_ids = torch.randint(1, vocab_size, (batch_size, seq_length))
    attention_mask = torch.ones(batch_size, seq_length)
    attention_mask[:, 7:] = 0  # 模拟padding
    
    print(f"输入形状: {input_ids.shape}")
    print(f"注意力掩码形状: {attention_mask.shape}")
    
    # 测试标准编码器
    print(f"\n🔄 测试标准编码器:")
    encoder = TransformerEncoder(
        vocab_size=vocab_size,
        d_model=d_model,
        n_heads=n_heads,
        n_layers=n_layers,
        d_ff=d_ff
    )
    
    output, all_hidden, all_attention = encoder(
        input_ids, 
        attention_mask,
        return_all_hidden_states=True,
        return_attention_weights=True
    )
    
    print(f"输出形状: {output.shape}")
    print(f"隐藏状态层数: {len(all_hidden)}")
    print(f"注意力权重层数: {len(all_attention)}")
    print(f"参数数量: {sum(p.numel() for p in encoder.parameters()):,}")
    
    # 测试带池化的编码器
    print(f"\n🔄 测试带池化的编码器:")
    pooled_encoder = PooledTransformerEncoder(
        vocab_size=vocab_size,
        d_model=d_model,
        n_heads=n_heads,
        n_layers=n_layers,
        d_ff=d_ff,
        pooling_strategy="mean"
    )
    
    seq_output, pooled_output = pooled_encoder(input_ids, attention_mask)
    print(f"序列输出形状: {seq_output.shape}")
    print(f"池化输出形状: {pooled_output.shape}")
    
    print("✅ Transformer编码器测试完成!")
