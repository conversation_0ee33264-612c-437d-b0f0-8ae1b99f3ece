"""
训练器模块
实现Transformer模型的训练逻辑

包含功能：
- 训练循环
- 验证循环
- 学习率调度
- 梯度裁剪
- 模型保存和加载
- 训练监控和日志
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import os
import time
import yaml
from pathlib import Path
from typing import Dict, Optional, Tuple, Any
from tqdm import tqdm
import math

# 设置代理
from utils import setup_proxy
setup_proxy()


class WarmupLRScheduler:
    """
    带预热的学习率调度器
    
    实现Transformer论文中的学习率调度策略：
    lr = d_model^(-0.5) * min(step^(-0.5), step * warmup_steps^(-1.5))
    """
    
    def __init__(self, optimizer: optim.Optimizer, d_model: int, warmup_steps: int = 4000):
        """
        初始化学习率调度器

        Args:
            optimizer: 优化器
            d_model: 模型维度
            warmup_steps: 预热步数
        """
        self.optimizer = optimizer
        self.d_model = d_model
        self.warmup_steps = warmup_steps
        self.step_num = 1  # 从1开始，避免0的负数次幂

        # 设置初始学习率
        self._update_lr()
    
    def step(self):
        """更新学习率"""
        self.step_num += 1
        self._update_lr()
    
    def _update_lr(self):
        """计算并更新学习率"""
        lr = self.d_model ** (-0.5) * min(
            self.step_num ** (-0.5),
            self.step_num * self.warmup_steps ** (-1.5)
        )
        
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = lr
    
    def get_lr(self) -> float:
        """获取当前学习率"""
        return self.optimizer.param_groups[0]['lr']


class Trainer:
    """
    Transformer训练器
    
    支持分类和序列到序列任务
    """
    
    def __init__(
        self,
        model: nn.Module,
        train_loader: DataLoader,
        val_loader: DataLoader,
        config: Dict[str, Any],
        device: torch.device,
        task_type: str = "translation"  # "classification" or "translation"
    ):
        """
        初始化训练器
        
        Args:
            model: 要训练的模型
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            config: 配置字典
            device: 设备
            task_type: 任务类型
        """
        self.model = model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.config = config
        self.device = device
        self.task_type = task_type
        
        # 创建优化器
        self.optimizer = self._create_optimizer()
        
        # 创建学习率调度器
        if config['training'].get('use_warmup', True):
            self.scheduler = WarmupLRScheduler(
                self.optimizer,
                config['model']['d_model'],
                config['training']['warmup_steps']
            )
        else:
            self.scheduler = None
        
        # 创建损失函数
        self.criterion = self._create_criterion()
        
        # 训练状态
        self.current_epoch = 0
        self.global_step = 0
        self.best_val_loss = float('inf')
        
        # 创建日志目录
        self.log_dir = Path(config['logging']['log_dir'])
        self.model_save_dir = Path(config['logging']['model_save_dir'])
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.model_save_dir.mkdir(parents=True, exist_ok=True)
        
        # TensorBoard写入器
        if config['logging'].get('tensorboard', True):
            self.writer = SummaryWriter(self.log_dir / 'tensorboard')
        else:
            self.writer = None
        
        # 训练历史
        self.train_history = {
            'train_loss': [],
            'val_loss': [],
            'learning_rate': []
        }
        
        print(f"🚀 训练器初始化完成")
        print(f"   任务类型: {task_type}")
        print(f"   设备: {device}")
        print(f"   模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    def _create_optimizer(self) -> optim.Optimizer:
        """创建优化器"""
        optimizer_config = self.config['training']
        
        if optimizer_config['optimizer'].lower() == 'adam':
            return optim.Adam(
                self.model.parameters(),
                lr=optimizer_config['learning_rate'],
                betas=(optimizer_config['beta1'], optimizer_config['beta2']),
                eps=optimizer_config['eps'],
                weight_decay=optimizer_config['weight_decay']
            )
        elif optimizer_config['optimizer'].lower() == 'adamw':
            return optim.AdamW(
                self.model.parameters(),
                lr=optimizer_config['learning_rate'],
                betas=(optimizer_config['beta1'], optimizer_config['beta2']),
                eps=optimizer_config['eps'],
                weight_decay=optimizer_config['weight_decay']
            )
        else:
            raise ValueError(f"不支持的优化器: {optimizer_config['optimizer']}")
    
    def _create_criterion(self) -> nn.Module:
        """创建损失函数"""
        if self.task_type == "classification":
            return nn.CrossEntropyLoss()
        elif self.task_type == "translation":
            # 忽略padding token的损失
            return nn.CrossEntropyLoss(ignore_index=0)
        else:
            raise ValueError(f"不支持的任务类型: {self.task_type}")
    
    def train_epoch(self) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = len(self.train_loader)
        
        progress_bar = tqdm(
            self.train_loader, 
            desc=f"Epoch {self.current_epoch + 1}",
            leave=False
        )
        
        for batch_idx, batch in enumerate(progress_bar):
            # 将数据移到设备
            batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            
            # 前向传播
            loss = self._forward_step(batch)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            if self.config['training'].get('gradient_clip', 0) > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.config['training']['gradient_clip']
                )
            
            # 优化器步骤
            self.optimizer.step()
            
            # 学习率调度
            if self.scheduler is not None:
                self.scheduler.step()
            
            # 更新统计
            total_loss += loss.item()
            self.global_step += 1
            
            # 更新进度条
            current_lr = self.scheduler.get_lr() if self.scheduler else self.optimizer.param_groups[0]['lr']
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'lr': f'{current_lr:.2e}'
            })
            
            # 记录日志
            if self.global_step % self.config['logging']['print_every'] == 0:
                self._log_training_step(loss.item(), current_lr)
            
            # 保存模型
            if self.global_step % self.config['training']['save_every'] == 0:
                self.save_checkpoint(f'checkpoint_step_{self.global_step}.pt')
        
        avg_loss = total_loss / num_batches
        return avg_loss
    
    def _forward_step(self, batch: Dict[str, torch.Tensor]) -> torch.Tensor:
        """前向传播步骤"""
        if self.task_type == "classification":
            # 分类任务
            outputs = self.model(
                batch['input_ids'],
                attention_mask=batch['attention_mask']
            )
            # 假设模型返回 (sequence_output, pooled_output)
            if isinstance(outputs, tuple):
                logits = outputs[1]  # 使用池化输出
            else:
                logits = outputs
            
            loss = self.criterion(logits, batch['labels'])
            
        elif self.task_type == "translation":
            # 翻译任务
            logits = self.model(
                batch['src_input_ids'],
                batch['tgt_input_ids'],
                src_attention_mask=batch['src_attention_mask'],
                tgt_attention_mask=batch['tgt_attention_mask']
            )
            
            # 重塑logits和标签以计算损失
            # logits: [batch_size, seq_length, vocab_size]
            # labels: [batch_size, seq_length]
            logits = logits.view(-1, logits.size(-1))
            labels = batch['tgt_labels'].view(-1)
            
            loss = self.criterion(logits, labels)
        
        else:
            raise ValueError(f"不支持的任务类型: {self.task_type}")
        
        return loss
    
    def validate(self) -> float:
        """验证模型"""
        self.model.eval()
        total_loss = 0.0
        num_batches = len(self.val_loader)
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc="Validation", leave=False):
                # 将数据移到设备
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                # 前向传播
                loss = self._forward_step(batch)
                total_loss += loss.item()
        
        avg_loss = total_loss / num_batches
        return avg_loss
    
    def train(self):
        """完整的训练循环"""
        print(f"🎯 开始训练...")
        print(f"   训练轮数: {self.config['training']['num_epochs']}")
        print(f"   训练批次数: {len(self.train_loader)}")
        print(f"   验证批次数: {len(self.val_loader)}")
        
        for epoch in range(self.config['training']['num_epochs']):
            self.current_epoch = epoch
            
            # 训练
            train_loss = self.train_epoch()
            
            # 验证
            val_loss = self.validate()
            
            # 记录历史
            self.train_history['train_loss'].append(train_loss)
            self.train_history['val_loss'].append(val_loss)
            current_lr = self.scheduler.get_lr() if self.scheduler else self.optimizer.param_groups[0]['lr']
            self.train_history['learning_rate'].append(current_lr)
            
            # 打印epoch结果
            print(f"Epoch {epoch + 1}/{self.config['training']['num_epochs']}")
            print(f"  训练损失: {train_loss:.4f}")
            print(f"  验证损失: {val_loss:.4f}")
            print(f"  学习率: {current_lr:.2e}")
            
            # 记录到TensorBoard
            if self.writer:
                self.writer.add_scalar('Loss/Train', train_loss, epoch)
                self.writer.add_scalar('Loss/Validation', val_loss, epoch)
                self.writer.add_scalar('Learning_Rate', current_lr, epoch)
            
            # 保存最佳模型
            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                self.save_checkpoint('best_model.pt')
                print(f"  💾 保存最佳模型 (验证损失: {val_loss:.4f})")
            
            print("-" * 50)
        
        print("🎉 训练完成!")
        
        # 关闭TensorBoard写入器
        if self.writer:
            self.writer.close()
    
    def _log_training_step(self, loss: float, lr: float):
        """记录训练步骤"""
        if self.writer:
            self.writer.add_scalar('Loss/Train_Step', loss, self.global_step)
            self.writer.add_scalar('Learning_Rate_Step', lr, self.global_step)
    
    def save_checkpoint(self, filename: str):
        """保存检查点"""
        checkpoint = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.__dict__ if self.scheduler else None,
            'current_epoch': self.current_epoch,
            'global_step': self.global_step,
            'best_val_loss': self.best_val_loss,
            'train_history': self.train_history,
            'config': self.config
        }
        
        torch.save(checkpoint, self.model_save_dir / filename)
    
    def load_checkpoint(self, filename: str):
        """加载检查点"""
        checkpoint_path = self.model_save_dir / filename
        if not checkpoint_path.exists():
            raise FileNotFoundError(f"检查点文件不存在: {checkpoint_path}")
        
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if self.scheduler and checkpoint['scheduler_state_dict']:
            self.scheduler.__dict__.update(checkpoint['scheduler_state_dict'])
        
        self.current_epoch = checkpoint['current_epoch']
        self.global_step = checkpoint['global_step']
        self.best_val_loss = checkpoint['best_val_loss']
        self.train_history = checkpoint['train_history']
        
        print(f"✅ 检查点加载成功: {filename}")


if __name__ == "__main__":
    # 测试训练器
    print("🧪 测试训练器模块...")
    
    # 这里只是基本的导入测试，实际训练需要在主训练脚本中进行
    print("✅ 训练器模块测试完成!")
