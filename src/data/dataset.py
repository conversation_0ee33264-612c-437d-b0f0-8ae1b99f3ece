"""
数据集处理模块
准备适合12GB显存的小型数据集

支持的数据集：
1. IMDB情感分析 - 用于编码器分类任务
2. WMT14 EN-DE (小型子集) - 用于序列到序列翻译任务
3. 合成数据 - 用于快速测试

数据预处理包括：
- 分词
- 构建词汇表
- 序列填充/截断
- 创建注意力掩码
"""

import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from datasets import load_dataset
from transformers import AutoTokenizer
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import pickle
import os
from pathlib import Path


class IMDBDataset(Dataset):
    """
    IMDB情感分析数据集
    
    用于训练编码器进行文本分类
    """
    
    def __init__(
        self,
        split: str = "train",
        max_length: int = 512,
        tokenizer_name: str = "bert-base-uncased",
        cache_dir: Optional[str] = None,
        subset_size: Optional[int] = None
    ):
        """
        初始化IMDB数据集
        
        Args:
            split: 数据集分割 ("train", "test")
            max_length: 最大序列长度
            tokenizer_name: 分词器名称
            cache_dir: 缓存目录
            subset_size: 子集大小（用于快速测试）
        """
        self.max_length = max_length
        self.split = split
        
        # 加载分词器
        self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_name)
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # 加载数据集
        print(f"📥 加载IMDB数据集 ({split})...")
        dataset = load_dataset("imdb", split=split, cache_dir=cache_dir)
        
        # 如果指定了子集大小，随机采样
        if subset_size is not None and subset_size < len(dataset):
            indices = np.random.choice(len(dataset), subset_size, replace=False)
            dataset = dataset.select(indices)
            print(f"   使用子集，大小: {len(dataset)}")
        
        self.texts = dataset["text"]
        self.labels = dataset["label"]
        
        print(f"   数据集大小: {len(self.texts)}")
        print(f"   标签分布: {np.bincount(self.labels)}")
    
    def __len__(self) -> int:
        return len(self.texts)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        text = self.texts[idx]
        label = self.labels[idx]
        
        # 分词和编码
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding="max_length",
            max_length=self.max_length,
            return_tensors="pt"
        )
        
        return {
            "input_ids": encoding["input_ids"].squeeze(0),
            "attention_mask": encoding["attention_mask"].squeeze(0),
            "labels": torch.tensor(label, dtype=torch.long)
        }


class TranslationDataset(Dataset):
    """
    翻译数据集
    
    用于训练完整的Transformer进行序列到序列任务
    """
    
    def __init__(
        self,
        src_lang: str = "en",
        tgt_lang: str = "de", 
        split: str = "train",
        max_length: int = 256,
        dataset_name: str = "wmt14",
        cache_dir: Optional[str] = None,
        subset_size: Optional[int] = 10000,
        build_vocab: bool = True
    ):
        """
        初始化翻译数据集
        
        Args:
            src_lang: 源语言
            tgt_lang: 目标语言
            split: 数据集分割
            max_length: 最大序列长度
            dataset_name: 数据集名称
            cache_dir: 缓存目录
            subset_size: 子集大小
            build_vocab: 是否构建词汇表
        """
        self.src_lang = src_lang
        self.tgt_lang = tgt_lang
        self.max_length = max_length
        self.split = split
        
        # 特殊token
        self.pad_token = "<pad>"
        self.unk_token = "<unk>"
        self.bos_token = "<bos>"
        self.eos_token = "<eos>"
        
        # 加载数据集
        print(f"📥 加载翻译数据集 {dataset_name} {src_lang}-{tgt_lang} ({split})...")
        
        try:
            # 尝试加载WMT14数据集
            dataset = load_dataset(
                dataset_name, 
                f"{src_lang}-{tgt_lang}",
                split=split,
                cache_dir=cache_dir
            )
        except:
            # 如果失败，使用合成数据
            print("   无法加载WMT14，使用合成数据...")
            dataset = self._create_synthetic_data(subset_size or 1000)
        
        # 提取文本对
        if hasattr(dataset, 'select') and subset_size is not None:
            if subset_size < len(dataset):
                indices = np.random.choice(len(dataset), subset_size, replace=False)
                dataset = dataset.select(indices)
        
        # 提取源语言和目标语言文本
        if isinstance(dataset, dict):
            # 合成数据格式
            self.src_texts = dataset["src"]
            self.tgt_texts = dataset["tgt"]
        else:
            # HuggingFace数据集格式
            self.src_texts = [item["translation"][src_lang] for item in dataset]
            self.tgt_texts = [item["translation"][tgt_lang] for item in dataset]
        
        print(f"   数据集大小: {len(self.src_texts)}")
        
        # 构建词汇表
        if build_vocab:
            self.src_vocab, self.tgt_vocab = self._build_vocabularies()
        else:
            self.src_vocab = self.tgt_vocab = None
    
    def _create_synthetic_data(self, size: int) -> Dict[str, List[str]]:
        """创建合成翻译数据"""
        print(f"   生成 {size} 条合成翻译数据...")
        
        # 简单的英语-德语词汇映射
        en_de_pairs = [
            ("hello", "hallo"),
            ("world", "welt"),
            ("good", "gut"),
            ("morning", "morgen"),
            ("evening", "abend"),
            ("thank", "danke"),
            ("you", "ihnen"),
            ("please", "bitte"),
            ("yes", "ja"),
            ("no", "nein"),
            ("water", "wasser"),
            ("food", "essen"),
            ("house", "haus"),
            ("car", "auto"),
            ("book", "buch"),
            ("time", "zeit"),
            ("day", "tag"),
            ("night", "nacht"),
            ("love", "liebe"),
            ("friend", "freund")
        ]
        
        src_texts = []
        tgt_texts = []
        
        for i in range(size):
            # 随机选择1-5个词组成句子
            num_words = np.random.randint(1, 6)
            selected_pairs = np.random.choice(len(en_de_pairs), num_words, replace=True)
            
            src_words = [en_de_pairs[idx][0] for idx in selected_pairs]
            tgt_words = [en_de_pairs[idx][1] for idx in selected_pairs]
            
            src_texts.append(" ".join(src_words))
            tgt_texts.append(" ".join(tgt_words))
        
        return {"src": src_texts, "tgt": tgt_texts}
    
    def _build_vocabularies(self) -> Tuple[Dict[str, int], Dict[str, int]]:
        """构建源语言和目标语言词汇表"""
        print("🔨 构建词汇表...")
        
        def build_vocab(texts: List[str], min_freq: int = 2) -> Dict[str, int]:
            # 统计词频
            word_freq = {}
            for text in texts:
                words = text.lower().split()
                for word in words:
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            # 构建词汇表
            vocab = {
                self.pad_token: 0,
                self.unk_token: 1,
                self.bos_token: 2,
                self.eos_token: 3
            }
            
            # 添加高频词
            for word, freq in word_freq.items():
                if freq >= min_freq:
                    vocab[word] = len(vocab)
            
            return vocab
        
        src_vocab = build_vocab(self.src_texts)
        tgt_vocab = build_vocab(self.tgt_texts)
        
        print(f"   源语言词汇表大小: {len(src_vocab)}")
        print(f"   目标语言词汇表大小: {len(tgt_vocab)}")
        
        return src_vocab, tgt_vocab
    
    def text_to_ids(self, text: str, vocab: Dict[str, int], add_special_tokens: bool = True) -> List[int]:
        """将文本转换为ID序列"""
        words = text.lower().split()
        
        ids = []
        if add_special_tokens:
            ids.append(vocab[self.bos_token])
        
        for word in words:
            ids.append(vocab.get(word, vocab[self.unk_token]))
        
        if add_special_tokens:
            ids.append(vocab[self.eos_token])
        
        return ids
    
    def __len__(self) -> int:
        return len(self.src_texts)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        src_text = self.src_texts[idx]
        tgt_text = self.tgt_texts[idx]
        
        # 转换为ID序列
        src_ids = self.text_to_ids(src_text, self.src_vocab, add_special_tokens=False)
        tgt_ids = self.text_to_ids(tgt_text, self.tgt_vocab, add_special_tokens=True)
        
        # 截断和填充
        src_ids = src_ids[:self.max_length]
        tgt_ids = tgt_ids[:self.max_length]
        
        # 创建输入和目标序列
        tgt_input = tgt_ids[:-1]  # 去掉最后的EOS作为输入
        tgt_output = tgt_ids[1:]  # 去掉开头的BOS作为目标
        
        # 填充
        src_ids += [self.src_vocab[self.pad_token]] * (self.max_length - len(src_ids))
        tgt_input += [self.tgt_vocab[self.pad_token]] * (self.max_length - 1 - len(tgt_input))
        tgt_output += [self.tgt_vocab[self.pad_token]] * (self.max_length - 1 - len(tgt_output))
        
        # 创建注意力掩码
        src_mask = [1 if id != self.src_vocab[self.pad_token] else 0 for id in src_ids]
        tgt_mask = [1 if id != self.tgt_vocab[self.pad_token] else 0 for id in tgt_input]
        
        return {
            "src_input_ids": torch.tensor(src_ids, dtype=torch.long),
            "tgt_input_ids": torch.tensor(tgt_input, dtype=torch.long),
            "tgt_labels": torch.tensor(tgt_output, dtype=torch.long),
            "src_attention_mask": torch.tensor(src_mask, dtype=torch.long),
            "tgt_attention_mask": torch.tensor(tgt_mask, dtype=torch.long)
        }


def create_dataloaders(
    dataset_type: str = "translation",
    batch_size: int = 16,
    max_length: int = 256,
    subset_size: Optional[int] = None,
    num_workers: int = 2
) -> Tuple[DataLoader, DataLoader]:
    """
    创建训练和验证数据加载器
    
    Args:
        dataset_type: 数据集类型 ("imdb", "translation")
        batch_size: 批次大小
        max_length: 最大序列长度
        subset_size: 子集大小
        num_workers: 工作进程数
        
    Returns:
        train_loader, val_loader: 训练和验证数据加载器
    """
    if dataset_type == "imdb":
        train_dataset = IMDBDataset(
            split="train",
            max_length=max_length,
            subset_size=subset_size
        )
        val_dataset = IMDBDataset(
            split="test",
            max_length=max_length,
            subset_size=subset_size // 4 if subset_size else None
        )
    
    elif dataset_type == "translation":
        train_dataset = TranslationDataset(
            split="train",
            max_length=max_length,
            subset_size=subset_size
        )
        # 使用训练数据的一部分作为验证集
        val_size = min(1000, len(train_dataset) // 10)
        val_indices = np.random.choice(len(train_dataset), val_size, replace=False)
        
        val_dataset = TranslationDataset(
            split="train",
            max_length=max_length,
            subset_size=val_size,
            build_vocab=False
        )
        val_dataset.src_vocab = train_dataset.src_vocab
        val_dataset.tgt_vocab = train_dataset.tgt_vocab
    
    else:
        raise ValueError(f"不支持的数据集类型: {dataset_type}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available()
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available()
    )
    
    return train_loader, val_loader


if __name__ == "__main__":
    # 测试数据集
    print("🧪 测试数据集模块...")
    
    # 测试IMDB数据集
    print(f"\n📊 测试IMDB数据集:")
    try:
        imdb_dataset = IMDBDataset(split="train", subset_size=100, max_length=128)
        sample = imdb_dataset[0]
        print(f"样本键: {sample.keys()}")
        print(f"输入形状: {sample['input_ids'].shape}")
        print(f"标签: {sample['labels']}")
    except Exception as e:
        print(f"IMDB数据集测试失败: {e}")
    
    # 测试翻译数据集
    print(f"\n🌍 测试翻译数据集:")
    translation_dataset = TranslationDataset(subset_size=100, max_length=64)
    sample = translation_dataset[0]
    print(f"样本键: {sample.keys()}")
    print(f"源序列形状: {sample['src_input_ids'].shape}")
    print(f"目标序列形状: {sample['tgt_input_ids'].shape}")
    print(f"源词汇表大小: {len(translation_dataset.src_vocab)}")
    print(f"目标词汇表大小: {len(translation_dataset.tgt_vocab)}")
    
    # 测试数据加载器
    print(f"\n🔄 测试数据加载器:")
    train_loader, val_loader = create_dataloaders(
        dataset_type="translation",
        batch_size=4,
        max_length=64,
        subset_size=50
    )
    
    batch = next(iter(train_loader))
    print(f"批次键: {batch.keys()}")
    print(f"批次大小: {batch['src_input_ids'].shape[0]}")
    
    print("✅ 数据集模块测试完成!")
