#!/usr/bin/env python3
"""
测试中文字体显示
验证步骤4的中文字体是否正常工作
"""

import matplotlib
# 设置后端以避免PyCharm兼容性问题
matplotlib.use('TkAgg')  # 使用Tkinter后端

import matplotlib.pyplot as plt
import numpy as np
from src.utils.utils import setup_chinese_font_simple


def test_chinese_display():
    """测试中文字体显示"""
    print("🧪 测试中文字体显示...")

    # 设置中文字体
    setup_chinese_font_simple()

    # 创建测试图表
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))

    # 测试1：简单的中文标题
    ax = axes[0, 0]
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    ax.plot(x, y, 'b-', linewidth=2)
    ax.set_title('测试中文标题', fontsize=14, fontweight='bold')
    ax.set_xlabel('横坐标')
    ax.set_ylabel('纵坐标')
    ax.grid(True, alpha=0.3)

    # 测试2：热力图
    ax = axes[0, 1]
    data = np.random.randn(5, 5)
    im = ax.imshow(data, cmap='RdYlBu', aspect='auto')
    ax.set_title('注意力权重热力图', fontsize=14, fontweight='bold')
    ax.set_xlabel('Key位置')
    ax.set_ylabel('Query位置')
    plt.colorbar(im, ax=ax, shrink=0.6)

    # 测试3：文本说明
    ax = axes[1, 0]
    ax.text(0.5, 0.7, 'Attention(Q,K,V) =', ha='center', va='center',
            fontsize=14, fontweight='bold', transform=ax.transAxes)
    ax.text(0.5, 0.5, 'Softmax(QK^T/√d_k)V', ha='center', va='center',
            fontsize=16, fontweight='bold', transform=ax.transAxes,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue'))

    formula_text = """
    其中：
    - Q: 查询矩阵
    - K: 键矩阵  
    - V: 值矩阵
    - d_k: 键的维度
    """
    ax.text(0.5, 0.2, formula_text, ha='center', va='center',
            fontsize=10, transform=ax.transAxes,
            bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow'))
    ax.axis('off')
    ax.set_title('公式说明测试', fontsize=14, fontweight='bold')

    # 测试4：复杂中文
    ax = axes[1, 1]
    categories = ['参数效率', '性能', '训练速度', '推理速度', '内存使用']
    values = [0.8, 0.9, 0.7, 0.6, 0.8]
    bars = ax.bar(categories, values, color='skyblue', alpha=0.7)
    ax.set_title('模型性能对比', fontsize=14, fontweight='bold')
    ax.set_ylabel('性能指标')
    ax.tick_params(axis='x', rotation=45)

    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width() / 2., height,
                f'{value:.1f}', ha='center', va='bottom')

    plt.suptitle('中文字体显示测试', fontsize=16, fontweight='bold')
    plt.tight_layout()

    # 保存图片
    plt.savefig('test_chinese_font.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✅ 中文字体测试完成！")
    print("📁 测试图片已保存: test_chinese_font.png")
    print("👀 请查看弹出的图形窗口，检查中文是否正常显示")


def main():
    """主函数"""
    print("🧪 中文字体显示测试")
    print("=" * 50)
    print("这个测试将验证步骤4的中文字体是否正常工作")
    print()

    try:
        test_chinese_display()

        print("\n📋 检查清单：")
        print("□ 图表标题是否显示中文？")
        print("□ 坐标轴标签是否显示中文？")
        print("□ 公式说明是否显示中文？")
        print("□ 柱状图标签是否显示中文？")
        print()
        print("如果以上都正常显示，说明步骤4的中文字体已修复！")

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
