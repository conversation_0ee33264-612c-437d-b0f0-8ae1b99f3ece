#!/usr/bin/env python3
"""
模型测试和验证脚本
验证Transformer实现的正确性

测试内容：
1. 模型结构测试
2. 前向传播测试
3. 梯度计算测试
4. 内存使用测试
5. 性能基准测试
6. 与参考实现对比
"""

import torch
import torch.nn as nn
import time
import sys
from pathlib import Path



# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.transformer.transformer import Transformer
from src.transformer.encoder import TransformerEncoder
from src.transformer.attention import MultiHeadAttention
from src.transformer.positional_encoding import PositionalEncoding


class ModelTester:
    """模型测试器"""
    
    def __init__(self, device: torch.device = None):
        """
        初始化测试器
        
        Args:
            device: 测试设备
        """
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🧪 模型测试器初始化")
        print(f"   设备: {self.device}")
        
        # 测试配置
        self.test_config = {
            'vocab_size': 1000,
            'batch_size': 4,
            'seq_length': 32,
            'd_model': 512,
            'n_heads': 8,
            'n_layers': 6,
            'd_ff': 2048,
            'dropout': 0.1
        }
    
    def test_positional_encoding(self):
        """测试位置编码"""
        print("\n📐 测试位置编码...")
        
        d_model = self.test_config['d_model']
        seq_length = self.test_config['seq_length']
        batch_size = self.test_config['batch_size']
        
        # 创建位置编码
        pe = PositionalEncoding(d_model, max_length=1000, dropout=0.0)
        pe = pe.to(self.device)
        
        # 测试输入
        x = torch.randn(batch_size, seq_length, d_model).to(self.device)
        
        # 前向传播
        output = pe(x)
        
        # 验证
        assert output.shape == x.shape, f"位置编码输出形状错误: {output.shape} vs {x.shape}"
        
        # 验证位置编码的性质
        pe_matrix = pe.get_positional_encoding(seq_length).squeeze(0)
        
        # 检查不同位置的编码是否不同
        pos_0 = pe_matrix[0]
        pos_1 = pe_matrix[1]
        assert not torch.allclose(pos_0, pos_1), "不同位置的编码应该不同"
        
        print("   ✅ 位置编码测试通过")
        return True
    
    def test_attention(self):
        """测试多头注意力"""
        print("\n🔍 测试多头注意力...")
        
        d_model = self.test_config['d_model']
        n_heads = self.test_config['n_heads']
        seq_length = self.test_config['seq_length']
        batch_size = self.test_config['batch_size']
        
        # 创建注意力层
        attention = MultiHeadAttention(d_model, n_heads, dropout=0.0)
        attention = attention.to(self.device)
        
        # 测试输入
        x = torch.randn(batch_size, seq_length, d_model).to(self.device)
        
        # 自注意力
        output, attn_weights = attention(x, x, x, return_attention=True)
        
        # 验证输出形状
        assert output.shape == x.shape, f"注意力输出形状错误: {output.shape} vs {x.shape}"
        assert attn_weights.shape == (batch_size, n_heads, seq_length, seq_length), \
            f"注意力权重形状错误: {attn_weights.shape}"
        
        # 验证注意力权重和为1
        attn_sum = attn_weights.sum(dim=-1)
        expected_sum = torch.ones_like(attn_sum)
        assert torch.allclose(attn_sum, expected_sum, atol=1e-6), "注意力权重和应该为1"
        
        # 测试掩码
        mask = torch.ones(batch_size, seq_length, seq_length).to(self.device)
        mask[:, :, seq_length//2:] = 0  # 掩盖后半部分
        
        masked_output, masked_attn = attention(x, x, x, mask=mask, return_attention=True)
        
        # 验证掩码效果
        assert torch.allclose(masked_attn[:, :, :, seq_length//2:], 
                             torch.zeros_like(masked_attn[:, :, :, seq_length//2:]), 
                             atol=1e-6), "掩码位置的注意力权重应该为0"
        
        print("   ✅ 多头注意力测试通过")
        return True
    
    def test_encoder(self):
        """测试编码器"""
        print("\n🏗️  测试编码器...")
        
        config = self.test_config
        
        # 创建编码器
        encoder = TransformerEncoder(
            vocab_size=config['vocab_size'],
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            d_ff=config['d_ff'],
            dropout=0.0
        )
        encoder = encoder.to(self.device)
        
        # 测试输入
        input_ids = torch.randint(1, config['vocab_size'], 
                                 (config['batch_size'], config['seq_length'])).to(self.device)
        attention_mask = torch.ones_like(input_ids).to(self.device)
        
        # 前向传播
        output, all_hidden, all_attention = encoder(
            input_ids, 
            attention_mask=attention_mask,
            return_all_hidden_states=True,
            return_attention_weights=True
        )
        
        # 验证输出形状
        expected_shape = (config['batch_size'], config['seq_length'], config['d_model'])
        assert output.shape == expected_shape, f"编码器输出形状错误: {output.shape} vs {expected_shape}"
        
        # 验证隐藏状态数量
        assert len(all_hidden) == config['n_layers'] + 1, \
            f"隐藏状态数量错误: {len(all_hidden)} vs {config['n_layers'] + 1}"
        
        # 验证注意力权重数量
        assert len(all_attention) == config['n_layers'], \
            f"注意力权重数量错误: {len(all_attention)} vs {config['n_layers']}"
        
        print("   ✅ 编码器测试通过")
        return True
    
    def test_transformer(self):
        """测试完整的Transformer"""
        print("\n🤖 测试完整Transformer...")
        
        config = self.test_config
        
        # 创建Transformer
        model = Transformer(
            src_vocab_size=config['vocab_size'],
            tgt_vocab_size=config['vocab_size'],
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_encoder_layers=config['n_layers'],
            n_decoder_layers=config['n_layers'],
            d_ff=config['d_ff'],
            dropout=0.0
        )
        model = model.to(self.device)
        
        # 测试输入
        src_input_ids = torch.randint(1, config['vocab_size'], 
                                     (config['batch_size'], config['seq_length'])).to(self.device)
        tgt_input_ids = torch.randint(1, config['vocab_size'], 
                                     (config['batch_size'], config['seq_length'])).to(self.device)
        
        # 前向传播
        logits = model(src_input_ids, tgt_input_ids)
        
        # 验证输出形状
        expected_shape = (config['batch_size'], config['seq_length'], config['vocab_size'])
        assert logits.shape == expected_shape, f"Transformer输出形状错误: {logits.shape} vs {expected_shape}"
        
        # 测试编码
        encoder_output = model.encode(src_input_ids)
        expected_encoder_shape = (config['batch_size'], config['seq_length'], config['d_model'])
        assert encoder_output.shape == expected_encoder_shape, \
            f"编码器输出形状错误: {encoder_output.shape} vs {expected_encoder_shape}"
        
        # 测试解码步骤
        step_logits = model.decode_step(tgt_input_ids[:, :5], encoder_output)
        expected_step_shape = (config['batch_size'], 1, config['vocab_size'])
        assert step_logits.shape == expected_step_shape, \
            f"解码步骤输出形状错误: {step_logits.shape} vs {expected_step_shape}"
        
        print("   ✅ 完整Transformer测试通过")
        return True
    
    def test_gradient_flow(self):
        """测试梯度流"""
        print("\n🌊 测试梯度流...")
        
        config = self.test_config
        
        # 创建模型
        model = Transformer(
            src_vocab_size=config['vocab_size'],
            tgt_vocab_size=config['vocab_size'],
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_encoder_layers=2,  # 使用较少层数以加快测试
            n_decoder_layers=2,
            d_ff=config['d_ff'],
            dropout=0.0
        )
        model = model.to(self.device)
        
        # 创建损失函数
        criterion = nn.CrossEntropyLoss()
        
        # 测试输入
        src_input_ids = torch.randint(1, config['vocab_size'], 
                                     (config['batch_size'], config['seq_length'])).to(self.device)
        tgt_input_ids = torch.randint(1, config['vocab_size'], 
                                     (config['batch_size'], config['seq_length'])).to(self.device)
        tgt_labels = torch.randint(1, config['vocab_size'], 
                                  (config['batch_size'], config['seq_length'])).to(self.device)
        
        # 前向传播
        logits = model(src_input_ids, tgt_input_ids)
        loss = criterion(logits.view(-1, config['vocab_size']), tgt_labels.view(-1))
        
        # 反向传播
        loss.backward()
        
        # 检查梯度
        has_grad = 0
        total_params = 0
        
        for name, param in model.named_parameters():
            total_params += 1
            if param.grad is not None:
                has_grad += 1
                # 检查梯度是否为NaN或无穷大
                assert not torch.isnan(param.grad).any(), f"参数 {name} 的梯度包含NaN"
                assert not torch.isinf(param.grad).any(), f"参数 {name} 的梯度包含无穷大"
        
        grad_ratio = has_grad / total_params
        assert grad_ratio > 0.9, f"梯度覆盖率过低: {grad_ratio:.2%}"
        
        print(f"   梯度覆盖率: {grad_ratio:.2%}")
        print("   ✅ 梯度流测试通过")
        return True
    
    def test_memory_usage(self):
        """测试内存使用"""
        print("\n💾 测试内存使用...")
        
        if not torch.cuda.is_available():
            print("   ⏭️  跳过内存测试（CPU模式）")
            return True
        
        config = self.test_config
        
        # 记录初始内存
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated()
        
        # 创建模型
        model = Transformer(
            src_vocab_size=config['vocab_size'],
            tgt_vocab_size=config['vocab_size'],
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_encoder_layers=config['n_layers'],
            n_decoder_layers=config['n_layers'],
            d_ff=config['d_ff'],
            dropout=0.0
        )
        model = model.to(self.device)
        
        model_memory = torch.cuda.memory_allocated() - initial_memory
        
        # 前向传播
        src_input_ids = torch.randint(1, config['vocab_size'], 
                                     (config['batch_size'], config['seq_length'])).to(self.device)
        tgt_input_ids = torch.randint(1, config['vocab_size'], 
                                     (config['batch_size'], config['seq_length'])).to(self.device)
        
        logits = model(src_input_ids, tgt_input_ids)
        
        forward_memory = torch.cuda.memory_allocated() - initial_memory
        
        # 反向传播
        loss = logits.sum()
        loss.backward()
        
        backward_memory = torch.cuda.memory_allocated() - initial_memory
        
        print(f"   模型内存: {model_memory / 1024**2:.1f} MB")
        print(f"   前向传播内存: {forward_memory / 1024**2:.1f} MB")
        print(f"   反向传播内存: {backward_memory / 1024**2:.1f} MB")
        
        # 检查内存是否在合理范围内（12GB显存）
        max_memory_gb = 12
        current_memory_gb = backward_memory / 1024**3
        
        assert current_memory_gb < max_memory_gb * 0.8, \
            f"内存使用过高: {current_memory_gb:.2f}GB > {max_memory_gb * 0.8:.2f}GB"
        
        print("   ✅ 内存使用测试通过")
        return True
    
    def test_performance(self):
        """测试性能"""
        print("\n⚡ 测试性能...")
        
        config = self.test_config
        
        # 创建模型
        model = Transformer(
            src_vocab_size=config['vocab_size'],
            tgt_vocab_size=config['vocab_size'],
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_encoder_layers=config['n_layers'],
            n_decoder_layers=config['n_layers'],
            d_ff=config['d_ff'],
            dropout=0.0
        )
        model = model.to(self.device)
        model.eval()
        
        # 测试输入
        src_input_ids = torch.randint(1, config['vocab_size'], 
                                     (config['batch_size'], config['seq_length'])).to(self.device)
        tgt_input_ids = torch.randint(1, config['vocab_size'], 
                                     (config['batch_size'], config['seq_length'])).to(self.device)
        
        # 预热
        for _ in range(5):
            with torch.no_grad():
                _ = model(src_input_ids, tgt_input_ids)
        
        # 性能测试
        num_runs = 20
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(num_runs):
                _ = model(src_input_ids, tgt_input_ids)
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        end_time = time.time()
        
        avg_time = (end_time - start_time) / num_runs
        throughput = config['batch_size'] / avg_time
        
        print(f"   平均推理时间: {avg_time * 1000:.2f} ms")
        print(f"   吞吐量: {throughput:.2f} samples/sec")
        
        print("   ✅ 性能测试通过")
        return True
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始模型测试...")
        
        tests = [
            ("位置编码", self.test_positional_encoding),
            ("多头注意力", self.test_attention),
            ("编码器", self.test_encoder),
            ("完整Transformer", self.test_transformer),
            ("梯度流", self.test_gradient_flow),
            ("内存使用", self.test_memory_usage),
            ("性能", self.test_performance)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
                    print(f"   ❌ {test_name}测试失败")
            except Exception as e:
                failed += 1
                print(f"   ❌ {test_name}测试出错: {e}")
        
        print(f"\n📊 测试结果:")
        print(f"   通过: {passed}/{len(tests)}")
        print(f"   失败: {failed}/{len(tests)}")
        
        if failed == 0:
            print("🎉 所有测试通过!")
        else:
            print("⚠️  部分测试失败，请检查实现")
        
        return failed == 0


def main():
    """主函数"""
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建测试器
    tester = ModelTester(device)
    
    # 运行测试
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 模型验证完成，实现正确!")
    else:
        print("\n❌ 模型验证失败，请检查实现!")
        sys.exit(1)


if __name__ == "__main__":
    # 设置代理
    from src.utils.utils import setup_proxy
    setup_proxy()
    main()
