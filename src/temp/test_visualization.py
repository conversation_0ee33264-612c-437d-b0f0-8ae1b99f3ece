#!/usr/bin/env python3
"""
测试可视化功能
验证matplotlib在GUI环境下的工作情况
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))
from ..utils.utils import setup_chinese_font
from ..utils.utils import setup_proxy
from ..transformer.attention import MultiHeadAttention


def test_basic_plot():
    """测试基础绘图功能"""
    print("🎨 测试基础绘图功能...")
    
    # 创建简单的测试图
    fig, axes = plt.subplots(1, 2, figsize=(12, 5)) #这里的12,5 表示图片的宽和高，单位是英寸
    
    # 左图：简单的线图
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    axes[0].plot(x, y, 'b-', linewidth=2, label='sin(x)') # b-表示蓝色实线
    axes[0].set_title('测试线图')
    axes[0].set_xlabel('x')
    axes[0].set_ylabel('y')
    axes[0].legend() #表示添加图例
    axes[0].grid(True, alpha=0.3) #表示添加网格，alpha表示网格透明度
    
    # 右图：热力图
    data = np.random.randn(5, 5) #生成随机数据，5行5列
    im = axes[1].imshow(data, cmap='RdYlBu', aspect='auto') #绘制热力图，cmap表示颜色,rdylbu表示红黄蓝三色渐变,aspect表示宽高比
    axes[1].set_title('测试热力图')
    plt.colorbar(im, ax=axes[1]) #添加颜色条,出现在热力图右侧
    
    plt.tight_layout() #避免图片重叠，自动调整子图参数
    plt.savefig('test_plot.png', dpi=150, bbox_inches='tight') #保存图片，dpi表示分辨率，bbox_inches表示 tight表示自动调整子图参数
    plt.show()
    
    print("✅ 基础绘图测试完成！")
    print("   如果你看到了图形窗口，说明GUI显示正常工作")
    print("   图片已保存为: test_plot.png")

def test_attention_visualization():
    """测试注意力可视化"""
    print("\n🔍 测试注意力可视化...")
    
    # 创建小型注意力模型
    d_model = 64
    n_heads = 4
    seq_length = 6
    batch_size = 1
    
    attention = MultiHeadAttention(d_model, n_heads, dropout=0.0)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    attention = attention.to(device)
    
    # 创建测试输入
    x = torch.randn(batch_size, seq_length, d_model).to(device)
    
    # 计算注意力
    with torch.no_grad():
        output, weights = attention(x, x, x, return_attention=True)
    
    # 可视化注意力权重
    weights_np = weights.squeeze(0).detach().cpu().numpy()  # [n_heads, seq_len, seq_len]
    
    fig, axes = plt.subplots(2, 2, figsize=(10, 8))
    axes = axes.flatten()
    
    for i in range(n_heads):
        sns.heatmap(weights_np[i], annot=True, fmt='.3f', cmap='Blues', 
                   ax=axes[i], cbar=True)
        axes[i].set_title(f'注意力头 {i+1}')
        axes[i].set_xlabel('Key位置')
        axes[i].set_ylabel('Query位置')
    
    plt.suptitle('多头注意力权重可视化测试', fontsize=14, fontweight='bold')
    plt.tight_layout()
    plt.savefig('test_attention.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("✅ 注意力可视化测试完成！")
    print("   图片已保存为: test_attention.png")
    
    # 打印一些统计信息
    print(f"\n📊 注意力统计:")
    print(f"   注意力权重形状: {weights.shape}")
    print(f"   权重范围: [{weights.min().item():.4f}, {weights.max().item():.4f}]")
    print(f"   权重和验证: {weights.sum(dim=-1).mean().item():.6f} (应该接近1.0)")

def test_interactive_features():
    """测试交互式功能"""
    print("\n🎮 测试交互式功能...")
    
    # 创建交互式图表
    fig, ax = plt.subplots(figsize=(8, 6))
    
    # 生成一些有趣的数据
    t = np.linspace(0, 4*np.pi, 1000)
    y1 = np.sin(t) * np.exp(-t/10)
    y2 = np.cos(t) * np.exp(-t/8)
    
    line1, = ax.plot(t, y1, 'b-', linewidth=2, label='衰减正弦波', alpha=0.8)
    line2, = ax.plot(t, y2, 'r-', linewidth=2, label='衰减余弦波', alpha=0.8)
    
    ax.set_title('交互式图表测试', fontsize=14, fontweight='bold')
    ax.set_xlabel('时间')
    ax.set_ylabel('幅度')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 添加一些注释
    ax.annotate('峰值点', xy=(np.pi/2, np.sin(np.pi/2)*np.exp(-(np.pi/2)/10)), 
                xytext=(2, 0.5), fontsize=12,
                arrowprops=dict(arrowstyle='->', color='red', lw=1.5))
    
    plt.tight_layout()
    plt.savefig('test_interactive.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("✅ 交互式功能测试完成！")
    print("   图片已保存为: test_interactive.png")
    print("   你可以在图形窗口中:")
    print("   - 缩放图像 (鼠标滚轮)")
    print("   - 平移图像 (拖拽)")
    print("   - 保存图像 (工具栏)")

def main():
    """主函数"""
    print("🧪 Transformer可视化功能测试")
    print("="*50)
    print("这个测试将验证matplotlib在你的GUI环境下是否正常工作")
    print()
    
    try:
        # 检查设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🚀 使用设备: {device}")
        
        if torch.cuda.is_available():
            print(f"   GPU: {torch.cuda.get_device_name(0)}")
        
        print()
        
        # 运行测试
        test_basic_plot()
        
        input("\n按回车键继续下一个测试...")
        
        test_attention_visualization()
        
        input("\n按回车键继续下一个测试...")
        
        test_interactive_features()
        
        print("\n🎉 所有可视化测试完成！")
        print("如果你看到了所有的图形窗口，说明可视化功能正常工作。")
        print("现在你可以运行完整的教程了：")
        print("   python start_tutorial.py")
        print("   或者直接运行: python tutorial_scripts/step1_attention.py")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        print("请检查以下几点：")
        print("1. 是否安装了matplotlib: pip install matplotlib")
        print("2. 是否有GUI环境支持")
        print("3. 是否正确设置了显示环境变量")
        
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    setup_proxy()
    setup_chinese_font()
    main()
