# Transformer模型配置文件
# 考虑到12GB显存限制，使用较小的模型参数

model:
  # 模型基本参数
  vocab_size: 30000          # 词汇表大小
  d_model: 512               # 模型维度（嵌入维度）
  n_heads: 8                 # 多头注意力头数
  n_layers: 6                # 编码器和解码器层数
  d_ff: 2048                 # 前馈网络隐藏层维度
  max_seq_length: 512        # 最大序列长度
  dropout: 0.1               # Dropout概率
  
  # 位置编码参数
  max_position_embeddings: 512

# 训练参数
training:
  batch_size: 16             # 批次大小（适合12GB显存）
  learning_rate: 0.0001      # 学习率
  num_epochs: 10             # 训练轮数
  warmup_steps: 4000         # 学习率预热步数
  gradient_clip: 1.0         # 梯度裁剪
  save_every: 1000           # 每多少步保存一次模型
  
  # 优化器参数
  optimizer: "adam"
  beta1: 0.9
  beta2: 0.98
  eps: 0.000000001  # 1e-9 written as decimal to avoid YAML parsing issues
  weight_decay: 0.01

# 数据参数
data:
  # 使用小型数据集以适应显存限制
  dataset_name: "imdb"       # IMDB情感分析数据集
  max_length: 512            # 文本最大长度
  train_split: 0.8           # 训练集比例
  val_split: 0.1             # 验证集比例
  test_split: 0.1            # 测试集比例
  
# 设备配置
device:
  use_cuda: true             # 是否使用CUDA
  device_id: 0               # GPU设备ID
  mixed_precision: true      # 是否使用混合精度训练

# 日志和保存
logging:
  log_dir: "./logs"
  model_save_dir: "./models"
  tensorboard: true
  print_every: 100           # 每多少步打印一次日志
