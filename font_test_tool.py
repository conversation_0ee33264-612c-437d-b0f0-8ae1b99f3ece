#!/usr/bin/env python3
"""
中文字体测试工具
用于检查和测试系统中的中文字体支持情况
"""

import matplotlib
# 设置后端以避免PyCharm兼容性问题
matplotlib.use('TkAgg')

from utils import setup_chinese_font, setup_chinese_font_simple, check_chinese_font_support
import matplotlib.pyplot as plt
import numpy as np


def main():
    """主函数"""
    print("🎨 中文字体测试工具")
    print("=" * 50)

    while True:
        print("\n请选择操作：")
        print("1. 检查系统字体支持情况")
        print("2. 测试智能字体设置")
        print("3. 测试简化字体设置")
        print("4. 生成中文字体测试图表")
        print("5. 退出")

        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == '1':
            check_font_support()
        elif choice == '2':
            test_smart_font_setup()
        elif choice == '3':
            test_simple_font_setup()
        elif choice == '4':
            generate_test_chart()
        elif choice == '5':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重试")


def check_font_support():
    """检查字体支持情况"""
    print("\n🔍 检查系统字体支持情况...")

    result = check_chinese_font_support()

    if 'error' in result:
        print(f"❌ 检查失败: {result['error']}")
        return

    print(f"\n📊 字体统计:")
    print(f"   系统总字体数: {result['total_fonts']}")
    print(f"   可用中文字体数: {result['chinese_fonts_available']}")

    print(f"\n🎨 中文字体支持情况:")
    for font_name, info in result['supported_fonts'].items():
        status = "✅" if info['available'] else "❌"
        print(f"   {status} {font_name} - {info['description']}")

    if result['chinese_fonts_available'] == 0:
        print(f"\n⚠️  警告: 未找到常见的中文字体")
        print(f"   建议安装以下字体之一:")
        print(f"   - Windows: 系统自带 SimHei 或 Microsoft YaHei")
        print(f"   - Linux: sudo apt install fonts-wqy-microhei")
        print(f"   - macOS: 系统自带中文字体")

    # 显示前20个可用字体
    print(f"\n📝 系统中前20个字体:")
    for i, font in enumerate(result['all_fonts'][:20]):
        print(f"   {i + 1:2d}. {font}")

    if len(result['all_fonts']) > 20:
        print(f"   ... 还有 {len(result['all_fonts']) - 20} 个字体")


def test_smart_font_setup():
    """测试智能字体设置"""
    print("\n🧠 测试智能字体设置...")

    selected_font = setup_chinese_font()

    if selected_font:
        print(f"✅ 智能字体设置成功，选择了: {selected_font}")

        # 测试显示
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.text(0.5, 0.5, f'智能字体测试\n当前字体: {selected_font}\n中文显示测试: 你好世界！',
                ha='center', va='center', fontsize=16, transform=ax.transAxes,
                bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue'))
        ax.set_title('智能字体设置测试', fontsize=18, fontweight='bold')
        ax.axis('off')

        plt.tight_layout()
        plt.savefig('smart_font_test.png', dpi=150, bbox_inches='tight')
        plt.show()

        print("📁 测试图片已保存: smart_font_test.png")
    else:
        print("❌ 智能字体设置失败")


def test_simple_font_setup():
    """测试简化字体设置"""
    print("\n⚡ 测试简化字体设置...")

    success = setup_chinese_font_simple()

    if success:
        print("✅ 简化字体设置成功")

        # 测试显示
        fig, ax = plt.subplots(figsize=(8, 6))
        ax.text(0.5, 0.5, '简化字体测试\n中文显示测试: 你好世界！\n这是教程中使用的设置方式',
                ha='center', va='center', fontsize=16, transform=ax.transAxes,
                bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgreen'))
        ax.set_title('简化字体设置测试', fontsize=18, fontweight='bold')
        ax.axis('off')

        plt.tight_layout()
        plt.savefig('simple_font_test.png', dpi=150, bbox_inches='tight')
        plt.show()

        print("📁 测试图片已保存: simple_font_test.png")
    else:
        print("❌ 简化字体设置失败")


def generate_test_chart():
    """生成综合测试图表"""
    print("\n📊 生成综合中文字体测试图表...")

    # 使用简化字体设置
    setup_chinese_font_simple()

    # 创建综合测试图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 1. 线图测试
    ax = axes[0, 0]
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    ax.plot(x, y1, 'b-', label='正弦波', linewidth=2)
    ax.plot(x, y2, 'r--', label='余弦波', linewidth=2)
    ax.set_title('三角函数图像', fontsize=14, fontweight='bold')
    ax.set_xlabel('横坐标 (弧度)')
    ax.set_ylabel('纵坐标 (幅值)')
    ax.legend()
    ax.grid(True, alpha=0.3)

    # 2. 柱状图测试
    ax = axes[0, 1]
    categories = ['准确率', '召回率', 'F1分数', '精确率']
    values = [0.85, 0.78, 0.81, 0.88]
    bars = ax.bar(categories, values, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'])
    ax.set_title('模型性能指标', fontsize=14, fontweight='bold')
    ax.set_ylabel('分数')
    ax.set_ylim(0, 1)

    # 添加数值标签
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width() / 2., height + 0.01,
                f'{value:.2f}', ha='center', va='bottom')

    # 3. 热力图测试
    ax = axes[1, 0]
    data = np.random.randn(6, 6)
    im = ax.imshow(data, cmap='RdYlBu', aspect='auto')
    ax.set_title('注意力权重矩阵', fontsize=14, fontweight='bold')
    ax.set_xlabel('键位置')
    ax.set_ylabel('查询位置')

    # 添加数值标签
    for i in range(6):
        for j in range(6):
            text = ax.text(j, i, f'{data[i, j]:.1f}',
                           ha="center", va="center", color="black", fontsize=8)

    plt.colorbar(im, ax=ax, shrink=0.6)

    # 4. 文本说明测试
    ax = axes[1, 1]
    ax.text(0.5, 0.8, 'Transformer架构', ha='center', va='center',
            fontsize=18, fontweight='bold', transform=ax.transAxes)

    description = """
    核心组件：
    • 多头注意力机制
    • 位置编码
    • 前馈神经网络
    • 残差连接
    • 层归一化
    
    应用领域：
    ✓ 机器翻译
    ✓ 文本摘要  
    ✓ 问答系统
    ✓ 代码生成
    """

    ax.text(0.5, 0.4, description, ha='center', va='center',
            fontsize=12, transform=ax.transAxes,
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightyellow', alpha=0.8))

    ax.axis('off')

    plt.suptitle('中文字体综合测试图表', fontsize=20, fontweight='bold')
    plt.tight_layout()

    # 保存图片
    plt.savefig('comprehensive_font_test.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("✅ 综合测试图表生成完成！")
    print("📁 测试图片已保存: comprehensive_font_test.png")
    print("👀 请检查图表中的中文是否正常显示")


if __name__ == "__main__":
    main()
