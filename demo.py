#!/usr/bin/env python3
"""
Transformer模型演示脚本
展示训练好的模型如何进行推理

功能：
1. 加载训练好的模型
2. 文本分类演示（IMDB情感分析）
3. 序列到序列翻译演示
4. 交互式推理
5. 模型可视化
"""

import argparse
import torch
import torch.nn.functional as F
import yaml
import sys
import os
from pathlib import Path
import numpy as np
from typing import Dict, List, Optional

# 设置代理
from utils import setup_proxy
setup_proxy()

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from transformer.transformer import Transformer
from transformer.encoder import PooledTransformerEncoder
from data.dataset import TranslationDataset
from train import ClassificationModel, load_config


class TransformerDemo:
    """Transformer演示器"""
    
    def __init__(self, config_path: str, model_path: str, task_type: str):
        """
        初始化演示器
        
        Args:
            config_path: 配置文件路径
            model_path: 模型检查点路径
            task_type: 任务类型 ("classification" or "translation")
        """
        self.config = load_config(config_path)
        self.task_type = task_type
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        print(f"🎭 Transformer演示器初始化")
        print(f"   任务类型: {task_type}")
        print(f"   设备: {self.device}")
        
        # 加载模型
        self.model, self.dataset = self._load_model(model_path)
        self.model.eval()
        
        print(f"   模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
    
    def _load_model(self, model_path: str):
        """加载模型"""
        print(f"📥 加载模型: {model_path}")
        
        if not Path(model_path).exists():
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        checkpoint = torch.load(model_path, map_location=self.device)
        
        if self.task_type == "classification":
            # 分类模型
            model = ClassificationModel(self.config, num_classes=2)
            dataset = None
            
        elif self.task_type == "translation":
            # 翻译模型 - 需要先创建数据集以获取词汇表
            dataset = TranslationDataset(subset_size=100, max_length=64)  # 小数据集用于获取词汇表
            
            model = Transformer(
                src_vocab_size=len(dataset.src_vocab),
                tgt_vocab_size=len(dataset.tgt_vocab),
                d_model=self.config['model']['d_model'],
                n_heads=self.config['model']['n_heads'],
                n_encoder_layers=self.config['model']['n_layers'],
                n_decoder_layers=self.config['model']['n_layers'],
                d_ff=self.config['model']['d_ff'],
                max_length=self.config['model']['max_seq_length'],
                dropout=0.0,  # 推理时不使用dropout
                pad_token_id=0
            )
        
        else:
            raise ValueError(f"不支持的任务类型: {self.task_type}")
        
        # 加载模型权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(self.device)
        
        print(f"   ✅ 模型加载成功")
        return model, dataset
    
    def classify_text(self, text: str) -> Dict[str, float]:
        """
        文本分类推理
        
        Args:
            text: 输入文本
            
        Returns:
            分类结果字典
        """
        if self.task_type != "classification":
            raise ValueError("当前模型不支持分类任务")
        
        # 这里需要使用与训练时相同的分词器
        # 为了演示，我们使用简单的词汇映射
        print(f"🔍 分析文本: {text[:100]}...")
        
        # 模拟分词（实际应用中需要使用训练时的分词器）
        words = text.lower().split()[:self.config['data']['max_length']]
        
        # 简单的词汇映射（实际应用中需要使用训练时的词汇表）
        vocab = {word: i+1 for i, word in enumerate(set(words))}
        vocab['<pad>'] = 0
        
        # 转换为ID序列
        input_ids = [vocab.get(word, 1) for word in words]  # 1是UNK token
        
        # 填充到固定长度
        max_length = self.config['data']['max_length']
        if len(input_ids) < max_length:
            input_ids.extend([0] * (max_length - len(input_ids)))
        else:
            input_ids = input_ids[:max_length]
        
        # 创建注意力掩码
        attention_mask = [1 if id != 0 else 0 for id in input_ids]
        
        # 转换为张量
        input_ids = torch.tensor([input_ids], dtype=torch.long).to(self.device)
        attention_mask = torch.tensor([attention_mask], dtype=torch.long).to(self.device)
        
        # 推理
        with torch.no_grad():
            logits = self.model(input_ids, attention_mask)
            probabilities = F.softmax(logits, dim=-1)
        
        # 解析结果
        neg_prob = probabilities[0, 0].item()
        pos_prob = probabilities[0, 1].item()
        
        result = {
            'negative': neg_prob,
            'positive': pos_prob,
            'prediction': 'positive' if pos_prob > neg_prob else 'negative',
            'confidence': max(neg_prob, pos_prob)
        }
        
        return result
    
    def translate_text(self, text: str, max_length: int = 50) -> str:
        """
        文本翻译推理
        
        Args:
            text: 源语言文本
            max_length: 最大生成长度
            
        Returns:
            翻译结果
        """
        if self.task_type != "translation":
            raise ValueError("当前模型不支持翻译任务")
        
        print(f"🌍 翻译文本: {text}")
        
        # 将文本转换为ID序列
        src_ids = self.dataset.text_to_ids(text, self.dataset.src_vocab, add_special_tokens=False)
        
        # 填充和截断
        max_src_length = self.config['data']['max_length']
        if len(src_ids) > max_src_length:
            src_ids = src_ids[:max_src_length]
        
        # 填充
        src_ids += [self.dataset.src_vocab['<pad>']] * (max_src_length - len(src_ids))
        
        # 创建注意力掩码
        src_mask = [1 if id != self.dataset.src_vocab['<pad>'] else 0 for id in src_ids]
        
        # 转换为张量
        src_input_ids = torch.tensor([src_ids], dtype=torch.long).to(self.device)
        src_attention_mask = torch.tensor([src_mask], dtype=torch.long).to(self.device)
        
        # 编码源序列
        with torch.no_grad():
            encoder_output = self.model.encode(src_input_ids, src_attention_mask)
        
        # 解码生成目标序列
        generated_ids = [self.dataset.tgt_vocab['<bos>']]  # 开始token
        
        for _ in range(max_length):
            # 准备解码器输入
            tgt_input_ids = torch.tensor([generated_ids], dtype=torch.long).to(self.device)
            
            # 解码一步
            with torch.no_grad():
                logits = self.model.decode_step(tgt_input_ids, encoder_output, src_attention_mask)
                
                # 贪心解码：选择概率最高的token
                next_token_id = logits.argmax(dim=-1).item()
                
                # 如果生成了结束token，停止生成
                if next_token_id == self.dataset.tgt_vocab['<eos>']:
                    break
                
                generated_ids.append(next_token_id)
        
        # 将ID序列转换回文本
        id_to_word = {v: k for k, v in self.dataset.tgt_vocab.items()}
        generated_words = []
        
        for token_id in generated_ids[1:]:  # 跳过BOS token
            word = id_to_word.get(token_id, '<unk>')
            if word not in ['<pad>', '<eos>']:
                generated_words.append(word)
        
        translation = ' '.join(generated_words)
        return translation
    
    def interactive_demo(self):
        """交互式演示"""
        print(f"\n🎮 交互式演示模式")
        print(f"任务类型: {self.task_type}")
        print("输入 'quit' 退出")
        print("-" * 50)
        
        while True:
            try:
                if self.task_type == "classification":
                    text = input("\n请输入要分析的文本: ").strip()
                    if text.lower() == 'quit':
                        break
                    
                    if text:
                        result = self.classify_text(text)
                        print(f"\n📊 分析结果:")
                        print(f"   预测: {result['prediction']}")
                        print(f"   置信度: {result['confidence']:.3f}")
                        print(f"   正面情感: {result['positive']:.3f}")
                        print(f"   负面情感: {result['negative']:.3f}")
                
                elif self.task_type == "translation":
                    text = input("\n请输入要翻译的英文文本: ").strip()
                    if text.lower() == 'quit':
                        break
                    
                    if text:
                        translation = self.translate_text(text)
                        print(f"\n🌍 翻译结果: {translation}")
                
            except KeyboardInterrupt:
                print("\n\n👋 再见!")
                break
            except Exception as e:
                print(f"\n❌ 处理出错: {e}")
    
    def batch_demo(self, examples: List[str]):
        """批量演示"""
        print(f"\n📦 批量演示")
        print("-" * 50)
        
        for i, example in enumerate(examples, 1):
            print(f"\n示例 {i}: {example}")
            
            try:
                if self.task_type == "classification":
                    result = self.classify_text(example)
                    print(f"   结果: {result['prediction']} (置信度: {result['confidence']:.3f})")
                
                elif self.task_type == "translation":
                    translation = self.translate_text(example)
                    print(f"   翻译: {translation}")
                    
            except Exception as e:
                print(f"   ❌ 处理出错: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Transformer模型演示")
    parser.add_argument("--config", type=str, default="config.yaml", help="配置文件路径")
    parser.add_argument("--model", type=str, required=True, help="模型检查点路径")
    parser.add_argument("--task", type=str, choices=["classification", "translation"], 
                       required=True, help="任务类型")
    parser.add_argument("--mode", type=str, choices=["interactive", "batch"], 
                       default="interactive", help="演示模式")
    
    args = parser.parse_args()
    
    try:
        # 创建演示器
        demo = TransformerDemo(args.config, args.model, args.task)
        
        if args.mode == "interactive":
            # 交互式演示
            demo.interactive_demo()
        
        elif args.mode == "batch":
            # 批量演示
            if args.task == "classification":
                examples = [
                    "This movie is absolutely fantastic! I loved every minute of it.",
                    "Terrible film, waste of time and money.",
                    "The movie was okay, nothing special but not bad either.",
                    "Amazing acting and beautiful cinematography!",
                    "I fell asleep halfway through, very boring."
                ]
            else:  # translation
                examples = [
                    "hello world",
                    "good morning",
                    "thank you",
                    "how are you",
                    "have a nice day"
                ]
            
            demo.batch_demo(examples)
    
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
