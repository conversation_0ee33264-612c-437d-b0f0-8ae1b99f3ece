#!/usr/bin/env python3
"""
第三步：深度理解训练过程
详细演示Transformer的训练过程，包含梯度流、学习率调度和训练监控
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import sys
import os
from pathlib import Path
from tqdm import tqdm
import time

# 设置matplotlib为交互式显示
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib.font_manager as fm
import warnings

# 设置中文字体和样式
def setup_chinese_font():
    """设置中文字体，避免中文显示问题"""
    chinese_fonts = [
        'SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei',
        'Noto Sans CJK SC', 'Source Han Sans CN', 'DejaVu Sans'
    ]

    available_fonts = [f.name for f in fm.fontManager.ttflist]

    for font in chinese_fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font]
            break
    else:
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']

    plt.rcParams['axes.unicode_minus'] = False
    # 禁用字体警告
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

setup_chinese_font()
plt.style.use('default')

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
from utils import setup_proxy, setup_chinese_font_simple
from src.transformer.transformer import Transformer
from src.data.dataset import TranslationDataset
from src.training.trainer import WarmupLRScheduler
from torch.utils.data import DataLoader

setup_proxy()

class TrainingVisualizer:
    """训练过程可视化器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🎯 训练过程学习器初始化")
        print(f"   设备: {self.device}")
        
        # 设置中文字体
        setup_chinese_font_simple()
        
        # 训练历史记录
        self.training_history = {
            'losses': [],
            'learning_rates': [],
            'gradient_norms': [],
            'parameter_norms': [],
            'step_times': []
        }
    
    def step1_model_initialization(self):
        """第1步：模型初始化分析"""
        print("\n" + "="*60)
        print("📚 第1步：模型初始化与参数分析")
        print("="*60)
        
        # 创建小型模型用于演示
        model_config = {
            'src_vocab_size': 100,
            'tgt_vocab_size': 100,
            'd_model': 128,
            'n_heads': 4,
            'n_encoder_layers': 2,
            'n_decoder_layers': 2,
            'd_ff': 512,
            'dropout': 0.1
        }
        
        print(f"📊 模型配置:")
        for key, value in model_config.items():
            print(f"   {key}: {value}")
        
        # 创建模型
        model = Transformer(**model_config).to(self.device)
        
        # 分析模型结构
        print(f"\n🏗️ 模型结构分析:")
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"   总参数数: {total_params:,}")
        print(f"   可训练参数: {trainable_params:,}")
        print(f"   模型大小: {total_params * 4 / 1024 / 1024:.2f} MB (float32)")
        
        # 分析各模块的参数分布
        print(f"\n📈 参数分布:")
        module_params = {}
        for name, module in model.named_modules():
            if len(list(module.children())) == 0:  # 叶子模块
                params = sum(p.numel() for p in module.parameters())
                if params > 0:
                    module_params[name] = params
        
        # 按参数数量排序
        sorted_modules = sorted(module_params.items(), key=lambda x: x[1], reverse=True)
        for name, params in sorted_modules[:10]:  # 显示前10个最大的模块
            percentage = params / total_params * 100
            print(f"   {name}: {params:,} ({percentage:.1f}%)")
        
        # 分析参数初始化
        print(f"\n🎲 参数初始化分析:")
        for name, param in model.named_parameters():
            if param.requires_grad:
                mean_val = param.data.mean().item()
                std_val = param.data.std().item()
                min_val = param.data.min().item()
                max_val = param.data.max().item()
                print(f"   {name}: 均值={mean_val:.4f}, 标准差={std_val:.4f}, 范围=[{min_val:.4f}, {max_val:.4f}]")
                if len(list(param.shape)) <= 2:  # 只显示前几个参数的详细信息
                    break
        
        return model
    
    def step2_forward_pass_analysis(self, model):
        """第2步：前向传播分析"""
        print("\n" + "="*60)
        print("📚 第2步：前向传播详细分析")
        print("="*60)
        
        # 创建示例输入
        batch_size = 2
        src_seq_len = 8
        tgt_seq_len = 6
        
        src_input = torch.randint(1, 100, (batch_size, src_seq_len)).to(self.device)
        tgt_input = torch.randint(1, 100, (batch_size, tgt_seq_len)).to(self.device)
        
        print(f"📊 输入数据:")
        print(f"   源序列: {src_input.shape}")
        print(f"   目标序列: {tgt_input.shape}")
        print(f"   源序列示例: {src_input[0].cpu().numpy()}")
        print(f"   目标序列示例: {tgt_input[0].cpu().numpy()}")
        
        # 详细跟踪前向传播
        print(f"\n⚙️  前向传播过程:")
        
        model.eval()
        with torch.no_grad():
            # 1. 编码器
            print(f"\n   1️⃣ 编码器处理:")
            encoder_output = model.encode(src_input)
            print(f"      编码器输出: {encoder_output.shape}")
            print(f"      输出统计: 均值={encoder_output.mean().item():.4f}, 标准差={encoder_output.std().item():.4f}")
            
            # 2. 解码器
            print(f"\n   2️⃣ 解码器处理:")
            decoder_output = model.decoder(tgt_input, encoder_output)
            print(f"      解码器输出: {decoder_output.shape}")
            print(f"      输出统计: 均值={decoder_output.mean().item():.4f}, 标准差={decoder_output.std().item():.4f}")
            
            # 3. 输出投影
            print(f"\n   3️⃣ 输出投影:")
            logits = model.output_projection(decoder_output)
            print(f"      Logits: {logits.shape}")
            print(f"      Logits统计: 均值={logits.mean().item():.4f}, 标准差={logits.std().item():.4f}")
            
            # 4. 概率分布
            print(f"\n   4️⃣ 概率分布:")
            probs = torch.softmax(logits, dim=-1)
            print(f"      概率分布: {probs.shape}")
            print(f"      概率和验证: {probs.sum(dim=-1)[0, 0].item():.6f}")
            
            # 分析预测
            predicted_ids = torch.argmax(logits, dim=-1)
            print(f"      预测ID: {predicted_ids[0].cpu().numpy()}")
        
        # 可视化激活分布
        self._visualize_activations(encoder_output, decoder_output, logits)
        
        return encoder_output, decoder_output, logits
    
    def step3_loss_computation(self, model):
        """第3步：损失计算分析"""
        print("\n" + "="*60)
        print("📚 第3步：损失计算与反向传播")
        print("="*60)
        
        # 创建训练数据
        batch_size = 4
        src_seq_len = 10
        tgt_seq_len = 8
        
        src_input = torch.randint(1, 100, (batch_size, src_seq_len)).to(self.device)
        tgt_input = torch.randint(1, 100, (batch_size, tgt_seq_len)).to(self.device)
        tgt_labels = torch.randint(1, 100, (batch_size, tgt_seq_len)).to(self.device)
        
        print(f"📊 训练数据:")
        print(f"   批次大小: {batch_size}")
        print(f"   源序列长度: {src_seq_len}")
        print(f"   目标序列长度: {tgt_seq_len}")
        
        # 损失函数
        criterion = nn.CrossEntropyLoss(ignore_index=0)
        
        print(f"\n⚙️  损失计算过程:")
        
        model.train()
        
        # 前向传播
        print(f"\n   1️⃣ 前向传播:")
        logits = model(src_input, tgt_input)
        print(f"      输出logits: {logits.shape}")
        
        # 重塑用于损失计算
        print(f"\n   2️⃣ 重塑数据:")
        logits_flat = logits.view(-1, logits.size(-1))
        labels_flat = tgt_labels.view(-1)
        print(f"      展平logits: {logits_flat.shape}")
        print(f"      展平标签: {labels_flat.shape}")
        
        # 计算损失
        print(f"\n   3️⃣ 损失计算:")
        loss = criterion(logits_flat, labels_flat)
        print(f"      交叉熵损失: {loss.item():.4f}")
        
        # 计算困惑度
        perplexity = torch.exp(loss)
        print(f"      困惑度: {perplexity.item():.4f}")
        
        # 分析预测准确性
        print(f"\n   4️⃣ 预测分析:")
        with torch.no_grad():
            predicted = torch.argmax(logits_flat, dim=-1)
            # 忽略padding token (0)
            valid_mask = labels_flat != 0
            if valid_mask.sum() > 0:
                accuracy = (predicted[valid_mask] == labels_flat[valid_mask]).float().mean()
                print(f"      预测准确率: {accuracy.item():.4f}")
                print(f"      有效token数: {valid_mask.sum().item()}")
        
        # 反向传播分析
        print(f"\n   5️⃣ 反向传播:")
        model.zero_grad()
        loss.backward()
        
        # 分析梯度
        total_grad_norm = 0
        param_count = 0
        grad_stats = {}
        
        for name, param in model.named_parameters():
            if param.grad is not None:
                grad_norm = param.grad.norm().item()
                total_grad_norm += grad_norm ** 2
                param_count += 1
                
                grad_stats[name] = {
                    'norm': grad_norm,
                    'mean': param.grad.mean().item(),
                    'std': param.grad.std().item()
                }
        
        total_grad_norm = total_grad_norm ** 0.5
        print(f"      总梯度范数: {total_grad_norm:.4f}")
        print(f"      有梯度的参数数: {param_count}")
        
        # 显示主要模块的梯度统计
        print(f"\n   📊 主要模块梯度统计:")
        important_modules = ['encoder.token_embedding', 'decoder.token_embedding', 'output_projection']
        for module_name in important_modules:
            for name, stats in grad_stats.items():
                if module_name in name and 'weight' in name:
                    print(f"      {name}: 范数={stats['norm']:.4f}, 均值={stats['mean']:.6f}")
                    break
        
        return loss, total_grad_norm
    
    def step4_learning_rate_schedule(self):
        """第4步：学习率调度分析"""
        print("\n" + "="*60)
        print("📚 第4步：学习率调度策略")
        print("="*60)
        
        # 创建简单模型用于演示
        model = nn.Linear(10, 1).to(self.device)
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        
        # 不同的学习率调度策略
        d_model = 512
        warmup_steps = 4000
        
        print(f"📊 学习率调度参数:")
        print(f"   模型维度: {d_model}")
        print(f"   预热步数: {warmup_steps}")
        
        # 1. Transformer原始调度
        print(f"\n⚙️  Transformer学习率调度:")
        scheduler = WarmupLRScheduler(optimizer, d_model, warmup_steps)
        
        steps = []
        lrs_transformer = []

        # 重置调度器到步骤1
        scheduler.step_num = 1
        scheduler._update_lr()

        for step in range(1, 10000, 100):
            scheduler.step_num = step
            scheduler._update_lr()
            lr = scheduler.get_lr()
            steps.append(step)
            lrs_transformer.append(lr)
        
        print(f"   步骤1的学习率: {lrs_transformer[0]:.8f}")
        print(f"   步骤{warmup_steps}的学习率: {lrs_transformer[warmup_steps//100-1]:.8f}")
        print(f"   步骤10000的学习率: {lrs_transformer[-1]:.8f}")
        
        # 2. 其他常见调度策略
        print(f"\n📈 其他调度策略对比:")
        
        # 常数学习率
        lrs_constant = [0.001] * len(steps)
        
        # 指数衰减
        lrs_exponential = [0.001 * (0.95 ** (step / 1000)) for step in steps]
        
        # 余弦退火
        lrs_cosine = [0.001 * (1 + np.cos(np.pi * step / 10000)) / 2 for step in steps]
        
        # 可视化学习率调度
        self._visualize_lr_schedules(steps, lrs_transformer, lrs_constant, lrs_exponential, lrs_cosine)
        
        return scheduler
    
    def step5_mini_training_loop(self):
        """第5步：迷你训练循环演示"""
        print("\n" + "="*60)
        print("📚 第5步：迷你训练循环演示")
        print("="*60)
        
        # 创建小型数据集
        print(f"📊 创建演示数据集:")
        dataset = TranslationDataset(subset_size=50, max_length=16, build_vocab=True)
        dataloader = DataLoader(dataset, batch_size=4, shuffle=True)
        
        print(f"   数据集大小: {len(dataset)}")
        print(f"   源词汇表大小: {len(dataset.src_vocab)}")
        print(f"   目标词汇表大小: {len(dataset.tgt_vocab)}")
        
        # 创建小型模型
        model = Transformer(
            src_vocab_size=len(dataset.src_vocab),
            tgt_vocab_size=len(dataset.tgt_vocab),
            d_model=64,
            n_heads=4,
            n_encoder_layers=2,
            n_decoder_layers=2,
            d_ff=256,
            dropout=0.1
        ).to(self.device)
        
        # 优化器和损失函数
        optimizer = optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss(ignore_index=0)
        
        print(f"\n⚙️  开始迷你训练:")
        print(f"   模型参数数: {sum(p.numel() for p in model.parameters()):,}")
        
        model.train()
        num_epochs = 3
        
        for epoch in range(num_epochs):
            print(f"\n   📅 Epoch {epoch + 1}/{num_epochs}:")
            epoch_loss = 0
            num_batches = 0
            
            for batch_idx, batch in enumerate(dataloader):
                if batch_idx >= 5:  # 只训练5个批次用于演示
                    break
                
                # 移动数据到设备
                src_ids = batch['src_input_ids'].to(self.device)
                tgt_ids = batch['tgt_input_ids'].to(self.device)
                labels = batch['tgt_labels'].to(self.device)
                
                # 记录时间
                start_time = time.time()
                
                # 前向传播
                optimizer.zero_grad()
                logits = model(src_ids, tgt_ids)
                
                # 计算损失
                loss = criterion(logits.view(-1, logits.size(-1)), labels.view(-1))
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
                
                # 优化器步骤
                optimizer.step()
                
                # 记录统计
                step_time = time.time() - start_time
                epoch_loss += loss.item()
                num_batches += 1
                
                # 记录训练历史
                self.training_history['losses'].append(loss.item())
                self.training_history['gradient_norms'].append(grad_norm.item())
                self.training_history['step_times'].append(step_time)
                
                print(f"      批次 {batch_idx + 1}: 损失={loss.item():.4f}, "
                      f"梯度范数={grad_norm.item():.4f}, 时间={step_time:.3f}s")
            
            avg_loss = epoch_loss / num_batches
            print(f"   平均损失: {avg_loss:.4f}")
        
        # 可视化训练过程
        self._visualize_training_progress()
        
        print(f"\n✅ 迷你训练完成!")
        return model
    
    def _visualize_activations(self, encoder_out, decoder_out, logits):
        """可视化激活分布"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 编码器输出
        encoder_flat = encoder_out.flatten().detach().cpu().numpy()
        axes[0].hist(encoder_flat, bins=50, alpha=0.7, color='blue')
        axes[0].set_title('编码器输出分布')
        axes[0].set_xlabel('激活值')
        axes[0].set_ylabel('频次')
        axes[0].grid(True, alpha=0.3)
        
        # 解码器输出
        decoder_flat = decoder_out.flatten().detach().cpu().numpy()
        axes[1].hist(decoder_flat, bins=50, alpha=0.7, color='green')
        axes[1].set_title('解码器输出分布')
        axes[1].set_xlabel('激活值')
        axes[1].set_ylabel('频次')
        axes[1].grid(True, alpha=0.3)
        
        # Logits
        logits_flat = logits.flatten().detach().cpu().numpy()
        axes[2].hist(logits_flat, bins=50, alpha=0.7, color='red')
        axes[2].set_title('Logits分布')
        axes[2].set_xlabel('Logits值')
        axes[2].set_ylabel('频次')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('tutorial_scripts/activations_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"   💾 激活分布图已保存: tutorial_scripts/activations_distribution.png")
        print("   👀 请查看弹出的图形窗口，关闭窗口后继续...")
    
    def _visualize_lr_schedules(self, steps, transformer, constant, exponential, cosine):
        """可视化学习率调度"""
        plt.figure(figsize=(12, 8))
        
        plt.subplot(2, 1, 1)
        plt.plot(steps, transformer, label='Transformer (Warmup)', linewidth=2)
        plt.plot(steps, constant, label='常数', linewidth=2)
        plt.plot(steps, exponential, label='指数衰减', linewidth=2)
        plt.plot(steps, cosine, label='余弦退火', linewidth=2)
        plt.xlabel('训练步数')
        plt.ylabel('学习率')
        plt.title('不同学习率调度策略对比')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 放大前5000步
        plt.subplot(2, 1, 2)
        mask = np.array(steps) <= 5000
        plt.plot(np.array(steps)[mask], np.array(transformer)[mask], label='Transformer (Warmup)', linewidth=2)
        plt.plot(np.array(steps)[mask], np.array(constant)[mask], label='常数', linewidth=2)
        plt.plot(np.array(steps)[mask], np.array(exponential)[mask], label='指数衰减', linewidth=2)
        plt.plot(np.array(steps)[mask], np.array(cosine)[mask], label='余弦退火', linewidth=2)
        plt.xlabel('训练步数')
        plt.ylabel('学习率')
        plt.title('前5000步学习率变化（放大）')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('tutorial_scripts/learning_rate_schedules.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"   💾 学习率调度图已保存: tutorial_scripts/learning_rate_schedules.png")
        print("   👀 请查看弹出的图形窗口，关闭窗口后继续...")
    
    def _visualize_training_progress(self):
        """可视化训练进度"""
        if not self.training_history['losses']:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # 损失曲线
        axes[0, 0].plot(self.training_history['losses'], color='blue', linewidth=2)
        axes[0, 0].set_title('训练损失')
        axes[0, 0].set_xlabel('步数')
        axes[0, 0].set_ylabel('损失')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 梯度范数
        axes[0, 1].plot(self.training_history['gradient_norms'], color='red', linewidth=2)
        axes[0, 1].set_title('梯度范数')
        axes[0, 1].set_xlabel('步数')
        axes[0, 1].set_ylabel('梯度范数')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 步骤时间
        axes[1, 0].plot(self.training_history['step_times'], color='green', linewidth=2)
        axes[1, 0].set_title('每步训练时间')
        axes[1, 0].set_xlabel('步数')
        axes[1, 0].set_ylabel('时间 (秒)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 损失分布
        axes[1, 1].hist(self.training_history['losses'], bins=20, alpha=0.7, color='purple')
        axes[1, 1].set_title('损失分布')
        axes[1, 1].set_xlabel('损失值')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('tutorial_scripts/training_progress.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"   💾 训练进度图已保存: tutorial_scripts/training_progress.png")
        print("   👀 请查看弹出的图形窗口，关闭窗口后继续...")


def main():
    """主函数：运行训练过程教程"""
    print("🎓 欢迎来到Transformer训练过程深度学习教程！")
    print("本教程将带你深入理解训练的每个细节")
    
    visualizer = TrainingVisualizer()
    
    try:
        # 第1步：模型初始化
        model = visualizer.step1_model_initialization()
        
        input("\n按回车键继续到第2步...")
        
        # 第2步：前向传播分析
        visualizer.step2_forward_pass_analysis(model)
        
        input("\n按回车键继续到第3步...")
        
        # 第3步：损失计算
        visualizer.step3_loss_computation(model)
        
        input("\n按回车键继续到第4步...")
        
        # 第4步：学习率调度
        visualizer.step4_learning_rate_schedule()
        
        input("\n按回车键继续到第5步...")
        
        # 第5步：迷你训练循环
        visualizer.step5_mini_training_loop()
        
        print("\n🎉 训练过程教程完成！")
        print("📁 所有可视化图片已保存在 tutorial_scripts/ 目录下")
        print("📚 现在你可以运行完整的训练: python quick_train.py")
        
    except KeyboardInterrupt:
        print("\n\n👋 教程被用户中断，再见！")


if __name__ == "__main__":
    main()
