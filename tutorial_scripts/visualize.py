#!/usr/bin/env python3
"""
Transformer可视化工具
提供各种可视化功能来帮助理解Transformer的工作原理
"""

import torch
import numpy as np
import sys
import os
from pathlib import Path

# 设置matplotlib为交互式显示
import matplotlib.pyplot as plt
import seaborn as sns
import networkx as nx
from matplotlib.patches import Rectangle, FancyBboxPatch

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
from utils import setup_proxy, setup_chinese_font_simple

setup_proxy()

class TransformerVisualizer:
    """Transformer可视化工具类"""
    
    def __init__(self):
        # 设置中文字体
        setup_chinese_font_simple()

        # 颜色方案
        self.colors = {
            'attention': '#FF6B6B',
            'encoder': '#4ECDC4',
            'decoder': '#45B7D1',
            'embedding': '#96CEB4',
            'feedforward': '#FFEAA7',
            'output': '#DDA0DD'
        }
    
    def visualize_transformer_architecture(self, save_path="tutorial_scripts/transformer_architecture.png"):
        """可视化Transformer整体架构"""
        fig, ax = plt.subplots(1, 1, figsize=(14, 10))
        
        # 定义组件位置和大小
        components = {
            # 编码器部分
            'input_embedding': {'pos': (1, 1), 'size': (2, 0.8), 'color': self.colors['embedding']},
            'pos_encoding': {'pos': (1, 2), 'size': (2, 0.8), 'color': self.colors['embedding']},
            'encoder_layers': {'pos': (1, 3.5), 'size': (2, 3), 'color': self.colors['encoder']},
            
            # 解码器部分
            'output_embedding': {'pos': (5, 1), 'size': (2, 0.8), 'color': self.colors['embedding']},
            'output_pos_encoding': {'pos': (5, 2), 'size': (2, 0.8), 'color': self.colors['embedding']},
            'decoder_layers': {'pos': (5, 3.5), 'size': (2, 3), 'color': self.colors['decoder']},
            
            # 输出部分
            'linear': {'pos': (5, 7), 'size': (2, 0.8), 'color': self.colors['output']},
            'softmax': {'pos': (5, 8), 'size': (2, 0.8), 'color': self.colors['output']},
        }
        
        # 绘制组件
        for name, info in components.items():
            x, y = info['pos']
            w, h = info['size']
            color = info['color']
            
            # 绘制矩形
            rect = FancyBboxPatch(
                (x, y), w, h,
                boxstyle="round,pad=0.1",
                facecolor=color,
                edgecolor='black',
                linewidth=1.5,
                alpha=0.8
            )
            ax.add_patch(rect)
            
            # 添加文本
            display_name = name.replace('_', ' ').title()
            ax.text(x + w/2, y + h/2, display_name, 
                   ha='center', va='center', fontsize=10, fontweight='bold')
        
        # 绘制连接线
        connections = [
            # 编码器流
            ((2, 1.8), (2, 2)),      # input -> pos
            ((2, 2.8), (2, 3.5)),    # pos -> encoder
            
            # 解码器流
            ((6, 1.8), (6, 2)),      # output -> pos
            ((6, 2.8), (6, 3.5)),    # pos -> decoder
            ((6, 6.5), (6, 7)),      # decoder -> linear
            ((6, 7.8), (6, 8)),      # linear -> softmax
            
            # 编码器到解码器的连接
            ((3, 5), (5, 5)),        # encoder -> decoder
        ]
        
        for start, end in connections:
            ax.annotate('', xy=end, xytext=start,
                       arrowprops=dict(arrowstyle='->', lw=2, color='black'))
        
        # 添加标签
        ax.text(2, 0.3, '输入序列\n(源语言)', ha='center', va='center', fontsize=12, fontweight='bold')
        ax.text(6, 0.3, '输出序列\n(目标语言)', ha='center', va='center', fontsize=12, fontweight='bold')
        ax.text(2, 7.5, '编码器', ha='center', va='center', fontsize=14, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor=self.colors['encoder'], alpha=0.7))
        ax.text(6, 9, '解码器', ha='center', va='center', fontsize=14, fontweight='bold',
                bbox=dict(boxstyle="round,pad=0.3", facecolor=self.colors['decoder'], alpha=0.7))
        
        ax.set_xlim(0, 8)
        ax.set_ylim(0, 10)
        ax.set_aspect('equal')
        ax.axis('off')
        ax.set_title('Transformer架构图', fontsize=16, fontweight='bold', pad=20)
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"💾 Transformer架构图已保存: {save_path}")
        print("👀 请查看弹出的图形窗口，关闭窗口后继续...")
    
    def visualize_attention_mechanism(self, save_path="tutorial_scripts/attention_mechanism.png"):
        """可视化注意力机制的计算过程"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 模拟数据
        seq_len = 5
        d_model = 4
        
        # 1. 输入序列
        ax = axes[0, 0]
        input_matrix = np.random.randn(seq_len, d_model)
        im = ax.imshow(input_matrix, cmap='RdYlBu', aspect='auto')
        ax.set_title('1. 输入序列 X\n[seq_len, d_model]', fontweight='bold')
        ax.set_xlabel('特征维度')
        ax.set_ylabel('序列位置')
        plt.colorbar(im, ax=ax, shrink=0.6)

        # 2. Q, K, V矩阵
        ax = axes[0, 1]
        qkv_data = np.random.randn(3, seq_len, d_model)
        colors = ['Reds', 'Greens', 'Blues']
        labels = ['Q (查询)', 'K (键)', 'V (值)']

        for i, (data, color, label) in enumerate(zip(qkv_data, colors, labels)):
            im = ax.imshow(data, cmap=color, alpha=0.7, aspect='auto',
                          extent=[i*d_model, (i+1)*d_model, 0, seq_len])

        ax.set_title('2. Q, K, V矩阵\n通过线性变换得到', fontweight='bold')
        ax.set_xlabel('特征维度')
        ax.set_ylabel('序列位置')
        ax.set_xticks([d_model/2, d_model*1.5, d_model*2.5])
        ax.set_xticklabels(['Q', 'K', 'V'])

        # 3. 注意力分数
        ax = axes[0, 2]
        scores = np.random.randn(seq_len, seq_len)
        im = ax.imshow(scores, cmap='RdYlBu', aspect='auto')
        ax.set_title('3. 注意力分数\nQK^T / √d_k', fontweight='bold')
        ax.set_xlabel('Key位置')
        ax.set_ylabel('Query位置')
        plt.colorbar(im, ax=ax, shrink=0.6)

        # 4. 注意力权重
        ax = axes[1, 0]
        weights = np.random.rand(seq_len, seq_len)
        weights = weights / weights.sum(axis=1, keepdims=True)  # 归一化
        im = ax.imshow(weights, cmap='Blues', aspect='auto')
        ax.set_title('4. 注意力权重\nSoftmax(分数)', fontweight='bold')
        ax.set_xlabel('Key位置')
        ax.set_ylabel('Query位置')
        plt.colorbar(im, ax=ax, shrink=0.6)

        # 5. 加权求和
        ax = axes[1, 1]
        output = np.random.randn(seq_len, d_model)
        im = ax.imshow(output, cmap='Greens', aspect='auto')
        ax.set_title('5. 输出\n权重 × V', fontweight='bold')
        ax.set_xlabel('特征维度')
        ax.set_ylabel('序列位置')
        plt.colorbar(im, ax=ax, shrink=0.6)
        
        # 6. 计算流程图
        ax = axes[1, 2]
        ax.text(0.5, 0.9, 'Attention(Q,K,V) =', ha='center', va='center',
                fontsize=14, fontweight='bold', transform=ax.transAxes)
        ax.text(0.5, 0.7, 'Softmax(QK^T/√d_k)V', ha='center', va='center',
                fontsize=16, fontweight='bold', transform=ax.transAxes,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue'))

        # 添加公式说明
        formula_text = """
        其中：
        - Q: 查询矩阵
        - K: 键矩阵
        - V: 值矩阵
        - d_k: 键的维度
        """
        ax.text(0.5, 0.4, formula_text, ha='center', va='center',
                fontsize=10, transform=ax.transAxes,
                bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow'))

        ax.axis('off')

        plt.suptitle('注意力机制计算过程', fontsize=18, fontweight='bold')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"💾 注意力机制图已保存: {save_path}")
        print("👀 请查看弹出的图形窗口，关闭窗口后继续...")
    
    def visualize_positional_encoding_patterns(self, d_model=512, max_len=100, 
                                             save_path="tutorial_scripts/positional_encoding_patterns.png"):
        """可视化位置编码的模式"""
        # 生成位置编码
        pe = np.zeros((max_len, d_model))
        position = np.arange(0, max_len, dtype=np.float32).reshape(-1, 1)
        
        div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))
        pe[:, 0::2] = np.sin(position * div_term)
        pe[:, 1::2] = np.cos(position * div_term)
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 位置编码热力图
        ax = axes[0, 0]
        im = ax.imshow(pe[:50, :64].T, cmap='RdYlBu', aspect='auto')
        ax.set_title('位置编码热力图\n(前50个位置，前64个维度)', fontweight='bold')
        ax.set_xlabel('位置')
        ax.set_ylabel('维度')
        plt.colorbar(im, ax=ax, shrink=0.6)
        
        # 2. 不同维度的波形
        ax = axes[0, 1]
        positions = np.arange(max_len)
        for i in range(0, min(8, d_model), 2):
            ax.plot(positions, pe[:, i], label=f'dim {i} (sin)', alpha=0.8)
            if i+1 < d_model:
                ax.plot(positions, pe[:, i+1], label=f'dim {i+1} (cos)', alpha=0.8)
        ax.set_title('不同维度的位置编码波形', fontweight='bold')
        ax.set_xlabel('位置')
        ax.set_ylabel('编码值')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 3. 位置相似性矩阵
        ax = axes[1, 0]
        # 计算位置间的余弦相似性
        pe_norm = pe / np.linalg.norm(pe, axis=1, keepdims=True)
        similarity = np.dot(pe_norm[:50], pe_norm[:50].T)
        im = ax.imshow(similarity, cmap='Blues', aspect='auto')
        ax.set_title('位置间余弦相似性\n(前50个位置)', fontweight='bold')
        ax.set_xlabel('位置')
        ax.set_ylabel('位置')
        plt.colorbar(im, ax=ax, shrink=0.6)
        
        # 4. 频率分析
        ax = axes[1, 1]
        frequencies = 1 / (10000 ** (np.arange(0, d_model, 2) / d_model))
        ax.semilogy(np.arange(0, d_model, 2), frequencies, 'bo-', markersize=4)
        ax.set_title('不同维度的频率', fontweight='bold')
        ax.set_xlabel('维度索引')
        ax.set_ylabel('频率 (log scale)')
        ax.grid(True, alpha=0.3)
        
        plt.suptitle('位置编码模式分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"💾 位置编码模式图已保存: {save_path}")
        print("👀 请查看弹出的图形窗口，关闭窗口后继续...")
    
    def visualize_training_dynamics(self, losses, learning_rates, gradient_norms,
                                  save_path="tutorial_scripts/training_dynamics.png"):
        """可视化训练动态"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        steps = range(len(losses))
        
        # 1. 损失曲线
        ax = axes[0, 0]
        ax.plot(steps, losses, color='red', linewidth=2, alpha=0.8)
        ax.set_title('训练损失', fontweight='bold')
        ax.set_xlabel('训练步数')
        ax.set_ylabel('损失值')
        ax.grid(True, alpha=0.3)

        # 添加趋势线
        if len(losses) > 10:
            z = np.polyfit(steps, losses, 1)
            p = np.poly1d(z)
            ax.plot(steps, p(steps), "--", alpha=0.8, color='darkred', label='趋势线')
            ax.legend()

        # 2. 学习率变化
        ax = axes[0, 1]
        ax.plot(steps, learning_rates, color='blue', linewidth=2, alpha=0.8)
        ax.set_title('学习率变化', fontweight='bold')
        ax.set_xlabel('训练步数')
        ax.set_ylabel('学习率')
        ax.grid(True, alpha=0.3)
        ax.ticklabel_format(style='scientific', axis='y', scilimits=(0,0))

        # 3. 梯度范数
        ax = axes[1, 0]
        ax.plot(steps, gradient_norms, color='green', linewidth=2, alpha=0.8)
        ax.set_title('梯度范数', fontweight='bold')
        ax.set_xlabel('训练步数')
        ax.set_ylabel('梯度范数')
        ax.grid(True, alpha=0.3)

        # 添加梯度爆炸/消失的警告线
        ax.axhline(y=10, color='red', linestyle='--', alpha=0.7, label='梯度爆炸警告')
        ax.axhline(y=0.01, color='orange', linestyle='--', alpha=0.7, label='梯度消失警告')
        ax.legend()

        # 4. 损失分布
        ax = axes[1, 1]
        ax.hist(losses, bins=20, alpha=0.7, color='purple', edgecolor='black')
        ax.set_title('损失值分布', fontweight='bold')
        ax.set_xlabel('损失值')
        ax.set_ylabel('频次')
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        mean_loss = np.mean(losses)
        std_loss = np.std(losses)
        ax.axvline(mean_loss, color='red', linestyle='-', linewidth=2, label=f'均值: {mean_loss:.3f}')
        ax.axvline(mean_loss + std_loss, color='orange', linestyle='--', label=f'+1σ: {mean_loss + std_loss:.3f}')
        ax.axvline(mean_loss - std_loss, color='orange', linestyle='--', label=f'-1σ: {mean_loss - std_loss:.3f}')
        ax.legend()

        plt.suptitle('训练动态分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"💾 训练动态图已保存: {save_path}")
        print("👀 请查看弹出的图形窗口，关闭窗口后继续...")
    
    def create_model_comparison_chart(self, models_data, save_path="tutorial_scripts/model_comparison.png"):
        """创建模型对比图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        model_names = list(models_data.keys())
        
        # 1. 参数数量对比
        ax = axes[0, 0]
        params = [models_data[name]['parameters'] for name in model_names]
        bars = ax.bar(model_names, params, color=plt.cm.Set3(np.linspace(0, 1, len(model_names))))
        ax.set_title('模型参数数量对比', fontweight='bold')
        ax.set_ylabel('参数数量 (百万)')
        ax.tick_params(axis='x', rotation=45)

        # 添加数值标签
        for bar, param in zip(bars, params):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{param:.1f}M', ha='center', va='bottom')

        # 2. 性能对比
        ax = axes[0, 1]
        performance = [models_data[name]['performance'] for name in model_names]
        bars = ax.bar(model_names, performance, color=plt.cm.Set2(np.linspace(0, 1, len(model_names))))
        ax.set_title('模型性能对比', fontweight='bold')
        ax.set_ylabel('性能指标')
        ax.tick_params(axis='x', rotation=45)

        for bar, perf in zip(bars, performance):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{perf:.3f}', ha='center', va='bottom')

        # 3. 训练时间对比
        ax = axes[1, 0]
        train_time = [models_data[name]['train_time'] for name in model_names]
        bars = ax.bar(model_names, train_time, color=plt.cm.Pastel1(np.linspace(0, 1, len(model_names))))
        ax.set_title('训练时间对比', fontweight='bold')
        ax.set_ylabel('训练时间 (小时)')
        ax.tick_params(axis='x', rotation=45)
        
        for bar, time in zip(bars, train_time):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{time:.1f}h', ha='center', va='bottom')
        
        # 4. 综合雷达图
        ax = axes[1, 1]
        categories = ['参数效率', '性能', '训练速度', '推理速度', '内存使用']
        
        # 归一化数据到0-1范围
        normalized_data = {}
        for name in model_names:
            data = models_data[name]
            normalized_data[name] = [
                1 - data['parameters'] / max(params),  # 参数越少越好
                data['performance'],  # 性能越高越好
                1 - data['train_time'] / max(train_time),  # 训练时间越短越好
                data.get('inference_speed', 0.5),  # 推理速度
                data.get('memory_efficiency', 0.5)  # 内存效率
            ]
        
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        for i, name in enumerate(model_names):
            values = normalized_data[name] + normalized_data[name][:1]
            ax.plot(angles, values, 'o-', linewidth=2, label=name, alpha=0.8)
            ax.fill(angles, values, alpha=0.25)
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        ax.set_ylim(0, 1)
        ax.set_title('模型综合对比雷达图', fontweight='bold')
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)

        plt.suptitle('Transformer模型对比分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"💾 模型对比图已保存: {save_path}")
        print("👀 请查看弹出的图形窗口，关闭窗口后继续...")


def main():
    """主函数：演示可视化工具"""
    print("🎨 Transformer可视化工具演示")
    
    visualizer = TransformerVisualizer()
    
    # 1. Transformer架构图
    print("\n1. 生成Transformer架构图...")
    visualizer.visualize_transformer_architecture()
    
    # 2. 注意力机制图
    print("\n2. 生成注意力机制图...")
    visualizer.visualize_attention_mechanism()
    
    # 3. 位置编码模式
    print("\n3. 生成位置编码模式图...")
    visualizer.visualize_positional_encoding_patterns()
    
    # 4. 训练动态（模拟数据）
    print("\n4. 生成训练动态图...")
    # 模拟训练数据
    steps = 100
    losses = [5.0 * np.exp(-i/30) + 0.5 + 0.1*np.random.randn() for i in range(steps)]
    lrs = [0.001 * min(i/10, 1) * (0.95 ** (i/20)) for i in range(steps)]
    grads = [2.0 * np.exp(-i/40) + 0.1 + 0.05*np.random.randn() for i in range(steps)]
    
    visualizer.visualize_training_dynamics(losses, lrs, grads)
    
    # 5. 模型对比（示例数据）
    print("\n5. 生成模型对比图...")
    models_data = {
        'Transformer-Small': {
            'parameters': 25.0,
            'performance': 0.85,
            'train_time': 2.5,
            'inference_speed': 0.8,
            'memory_efficiency': 0.9
        },
        'Transformer-Base': {
            'parameters': 110.0,
            'performance': 0.92,
            'train_time': 8.0,
            'inference_speed': 0.6,
            'memory_efficiency': 0.7
        },
        'Transformer-Large': {
            'parameters': 340.0,
            'performance': 0.95,
            'train_time': 24.0,
            'inference_speed': 0.4,
            'memory_efficiency': 0.5
        }
    }
    
    visualizer.create_model_comparison_chart(models_data)
    
    print("\n🎉 所有可视化图表已生成完成！")
    print("📁 图片保存在 tutorial_scripts/ 目录下")


if __name__ == "__main__":
    main()
