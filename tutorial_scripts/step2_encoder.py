#!/usr/bin/env python3
"""
第二步：深度理解编码器结构
详细演示编码器的工作原理，包含位置编码、编码器层和完整编码器
"""

import torch
import torch.nn as nn
import numpy as np
import sys
import os
from pathlib import Path

# 设置matplotlib为交互式显示
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib.font_manager as fm
import warnings


# 设置中文字体和样式
def setup_chinese_font():
    """设置中文字体，避免中文显示问题"""
    chinese_fonts = [
        'SimHei', 'Microsoft YaHei', 'WenQuanYi Micro Hei',
        'Noto Sans CJK SC', 'Source Han Sans CN', 'DejaVu Sans'
    ]

    available_fonts = [f.name for f in fm.fontManager.ttflist]

    for font in chinese_fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font]
            break
    else:
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']

    plt.rcParams['axes.unicode_minus'] = False
    # 禁用字体警告
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')


setup_chinese_font()
plt.style.use('default')

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
from utils import setup_proxy
from src.transformer.positional_encoding import PositionalEncoding
from src.transformer.encoder_layer import TransformerEncoderLayer
from src.transformer.encoder import TransformerEncoder

setup_proxy()


class EncoderVisualizer:
    """编码器可视化器"""

    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🏗️ 编码器学习器初始化")
        print(f"   设备: {self.device}")

        # 设置matplotlib
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

    def step1_positional_encoding(self):
        """第1步：位置编码详解"""
        print("\n" + "=" * 60)
        print("📚 第1步：位置编码原理与实现")
        print("=" * 60)

        # 参数设置
        d_model = 512
        max_length = 100

        print(f"📊 位置编码参数:")
        print(f"   模型维度: {d_model}")
        print(f"   最大长度: {max_length}")

        # 创建位置编码
        pe_layer = PositionalEncoding(d_model, max_length, dropout=0.0)

        print(f"\n⚙️  位置编码公式:")
        print(f"   PE(pos, 2i)   = sin(pos / 10000^(2i/d_model))")
        print(f"   PE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))")
        print(f"   其中 pos 是位置，i 是维度索引")

        # 手动计算几个位置的编码来验证
        print(f"\n🔍 手动计算验证:")
        pos = 0  # 位置0
        for i in range(3):  # 前3个维度对
            dim_2i = 2 * i
            dim_2i_1 = 2 * i + 1

            # 计算分母项
            div_term = 10000 ** (2 * i / d_model)

            # 计算sin和cos
            sin_val = np.sin(pos / div_term)
            cos_val = np.cos(pos / div_term)

            print(f"   维度 {dim_2i:3d} (sin): {sin_val:.6f}")
            print(f"   维度 {dim_2i_1:3d} (cos): {cos_val:.6f}")

        # 获取位置编码矩阵
        pe_matrix = pe_layer.pe.squeeze(0).detach().numpy()  # [max_length, d_model]

        print(f"\n📈 位置编码矩阵形状: {pe_matrix.shape}")
        print(f"   位置0的前10个维度: {pe_matrix[0, :10]}")
        print(f"   位置1的前10个维度: {pe_matrix[1, :10]}")

        # 可视化位置编码
        self._visualize_positional_encoding(pe_matrix, d_model, max_length)

        # 分析位置编码的性质
        self._analyze_pe_properties(pe_matrix)

        return pe_layer

    def step2_encoder_layer(self):
        """第2步：编码器层详解"""
        print("\n" + "=" * 60)
        print("📚 第2步：编码器层结构与计算")
        print("=" * 60)

        # 参数设置
        batch_size = 2
        seq_length = 8
        d_model = 256
        n_heads = 8
        d_ff = 1024

        print(f"📊 编码器层参数:")
        print(f"   批次大小: {batch_size}")
        print(f"   序列长度: {seq_length}")
        print(f"   模型维度: {d_model}")
        print(f"   注意力头数: {n_heads}")
        print(f"   前馈网络维度: {d_ff}")

        # 创建编码器层
        encoder_layer = TransformerEncoderLayer(
            d_model=d_model,
            n_heads=n_heads,
            d_ff=d_ff,
            dropout=0.1,
            norm_first=False  # Post-LN结构
        ).to(self.device)

        # 创建输入
        x = torch.randn(batch_size, seq_length, d_model).to(self.device)

        print(f"\n🔍 输入形状: {x.shape}")
        print(f"   输入统计: 均值={x.mean().item():.4f}, 标准差={x.std().item():.4f}")

        # 详细跟踪编码器层的计算过程
        print(f"\n⚙️  编码器层计算过程 (Post-LN):")
        print(f"   结构: 输入 → 自注意力 → 残差+归一化 → 前馈网络 → 残差+归一化 → 输出")

        with torch.no_grad():
            # 保存原始输入
            input_original = x.clone()

            # 1. 自注意力
            print(f"\n   1️⃣ 自注意力计算:")
            attn_output, attn_weights = encoder_layer.self_attention(
                x, x, x, return_attention=True
            )
            print(f"      注意力输出: {attn_output.shape}")
            print(f"      注意力权重: {attn_weights.shape}")
            print(f"      输出统计: 均值={attn_output.mean().item():.4f}, 标准差={attn_output.std().item():.4f}")

            # 2. 残差连接 + 层归一化
            print(f"\n   2️⃣ 第一个残差连接和层归一化:")
            residual_1 = x + encoder_layer.dropout(attn_output)
            norm_1 = encoder_layer.norm1(residual_1)
            print(f"      残差连接后: 均值={residual_1.mean().item():.4f}, 标准差={residual_1.std().item():.4f}")
            print(f"      层归一化后: 均值={norm_1.mean().item():.4f}, 标准差={norm_1.std().item():.4f}")

            # 3. 前馈网络
            print(f"\n   3️⃣ 前馈网络:")
            ff_output = encoder_layer.feed_forward(norm_1)
            print(f"      前馈网络输出: {ff_output.shape}")
            print(f"      输出统计: 均值={ff_output.mean().item():.4f}, 标准差={ff_output.std().item():.4f}")

            # 4. 第二个残差连接 + 层归一化
            print(f"\n   4️⃣ 第二个残差连接和层归一化:")
            residual_2 = norm_1 + encoder_layer.dropout(ff_output)
            final_output = encoder_layer.norm2(residual_2)
            print(f"      残差连接后: 均值={residual_2.mean().item():.4f}, 标准差={residual_2.std().item():.4f}")
            print(f"      最终输出: 均值={final_output.mean().item():.4f}, 标准差={final_output.std().item():.4f}")

        # 使用官方接口验证
        official_output, official_attn = encoder_layer(x, return_attention=True)

        print(f"\n✅ 验证结果:")
        print(f"   手动计算输出: {final_output.shape}")
        print(f"   官方接口输出: {official_output.shape}")
        print(f"   输出差异: {torch.max(torch.abs(final_output - official_output)).item():.6f}")

        # 可视化编码器层的变换过程
        self._visualize_encoder_layer_flow(
            input_original, attn_output, residual_1, norm_1,
            ff_output, residual_2, final_output
        )

        # 分析注意力模式
        self._analyze_attention_patterns(official_attn)

        return encoder_layer, official_output

    def step3_complete_encoder(self):
        """第3步：完整编码器"""
        print("\n" + "=" * 60)
        print("📚 第3步：完整编码器结构")
        print("=" * 60)

        # 参数设置
        vocab_size = 1000
        batch_size = 2
        seq_length = 10
        d_model = 256
        n_heads = 8
        n_layers = 4
        d_ff = 1024

        print(f"📊 完整编码器参数:")
        print(f"   词汇表大小: {vocab_size}")
        print(f"   模型维度: {d_model}")
        print(f"   层数: {n_layers}")
        print(f"   注意力头数: {n_heads}")

        # 创建完整编码器
        encoder = TransformerEncoder(
            vocab_size=vocab_size,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            d_ff=d_ff,
            dropout=0.1
        ).to(self.device)

        # 创建输入序列
        input_ids = torch.randint(1, vocab_size, (batch_size, seq_length)).to(self.device)
        attention_mask = torch.ones_like(input_ids).to(self.device)
        attention_mask[:, 7:] = 0  # 模拟padding

        print(f"\n🔍 输入数据:")
        print(f"   输入ID: {input_ids.shape}")
        print(f"   注意力掩码: {attention_mask.shape}")
        print(f"   第一个样本: {input_ids[0].cpu().numpy()}")
        print(f"   第一个掩码: {attention_mask[0].cpu().numpy()}")

        # 详细跟踪编码器的计算过程
        print(f"\n⚙️  完整编码器计算过程:")

        with torch.no_grad():
            # 1. Token嵌入
            embeddings = encoder.token_embedding(input_ids) * encoder.embed_scale
            print(f"\n   1️⃣ Token嵌入:")
            print(f"      嵌入形状: {embeddings.shape}")
            print(f"      缩放因子: {encoder.embed_scale:.4f}")
            print(f"      嵌入统计: 均值={embeddings.mean().item():.4f}, 标准差={embeddings.std().item():.4f}")

            # 2. 位置编码
            pos_encoded = encoder.positional_encoding(embeddings)
            print(f"\n   2️⃣ 位置编码:")
            print(f"      位置编码后: {pos_encoded.shape}")
            print(f"      统计: 均值={pos_encoded.mean().item():.4f}, 标准差={pos_encoded.std().item():.4f}")

            # 3. 通过各个编码器层
            x = pos_encoded
            layer_outputs = [x]
            layer_attentions = []

            # 创建扩展的注意力掩码
            extended_mask = encoder._create_extended_attention_mask(attention_mask)

            for i, layer in enumerate(encoder.layers):
                print(f"\n   3️⃣ 编码器层 {i + 1}:")
                x, attn = layer(x, mask=extended_mask, return_attention=True)
                layer_outputs.append(x)
                layer_attentions.append(attn)
                print(f"      层{i + 1}输出: 均值={x.mean().item():.4f}, 标准差={x.std().item():.4f}")

            # 4. 最终层归一化
            if encoder.final_norm is not None:
                final_output = encoder.final_norm(x)
                print(f"\n   4️⃣ 最终层归一化:")
                print(f"      最终输出: 均值={final_output.mean().item():.4f}, 标准差={final_output.std().item():.4f}")
            else:
                final_output = x

        # 使用官方接口验证
        official_output, all_hidden, all_attention = encoder(
            input_ids, attention_mask=attention_mask,
            return_all_hidden_states=True, return_attention_weights=True
        )

        print(f"\n✅ 验证结果:")
        print(f"   手动计算输出: {final_output.shape}")
        print(f"   官方接口输出: {official_output.shape}")
        print(f"   隐藏状态层数: {len(all_hidden)}")
        print(f"   注意力权重层数: {len(all_attention)}")
        print(f"   输出差异: {torch.max(torch.abs(final_output - official_output)).item():.6f}")

        # 可视化编码器的层级变化
        self._visualize_encoder_layers(layer_outputs, layer_attentions)

        # 分析编码器的表示学习
        self._analyze_representation_learning(all_hidden, input_ids, attention_mask)

        return encoder, official_output

    def step4_encoder_analysis(self):
        """第4步：编码器深度分析"""
        print("\n" + "=" * 60)
        print("📚 第4步：编码器深度分析")
        print("=" * 60)

        # 创建有意义的输入来分析
        sentences = [
            "The quick brown fox jumps over the lazy dog",
            "I love natural language processing and machine learning",
            "Transformer models are very powerful for NLP tasks"
        ]

        # 简单的分词和词汇表构建
        all_words = []
        for sentence in sentences:
            all_words.extend(sentence.lower().split())

        vocab = {'<pad>': 0, '<unk>': 1}
        for word in set(all_words):
            vocab[word] = len(vocab)

        print(f"📝 构建词汇表: {len(vocab)} 个词")

        # 转换句子
        max_length = 12
        sequences = []
        for sentence in sentences:
            words = sentence.lower().split()
            ids = [vocab.get(word, vocab['<unk>']) for word in words]
            if len(ids) < max_length:
                ids.extend([vocab['<pad>']] * (max_length - len(ids)))
            else:
                ids = ids[:max_length]
            sequences.append(ids)

        # 创建编码器
        encoder = TransformerEncoder(
            vocab_size=len(vocab),
            d_model=128,
            n_heads=4,
            n_layers=3,
            d_ff=512,
            dropout=0.0
        ).to(self.device)

        print(f"\n🔍 分析句子编码:")
        for i, (sentence, seq) in enumerate(zip(sentences, sequences)):
            print(f"\n   句子 {i + 1}: '{sentence}'")

            # 编码
            input_ids = torch.tensor([seq]).to(self.device)
            attention_mask = torch.tensor([[1 if id != vocab['<pad>'] else 0 for id in seq]]).to(self.device)

            output, all_hidden, all_attention = encoder(
                input_ids, attention_mask=attention_mask,
                return_all_hidden_states=True, return_attention_weights=True
            )

            # 分析每层的表示
            print(f"   各层表示变化:")
            for layer_idx, hidden in enumerate(all_hidden):
                layer_mean = hidden.mean().item()
                layer_std = hidden.std().item()
                print(f"     层{layer_idx}: 均值={layer_mean:.4f}, 标准差={layer_std:.4f}")

            # 分析注意力模式
            print(f"   注意力模式分析:")
            words = sentence.lower().split()
            valid_length = min(len(words), max_length)

            for layer_idx, attn in enumerate(all_attention):
                avg_attn = attn.mean(dim=1).squeeze(0)[:valid_length, :valid_length]
                max_attn_pos = torch.argmax(avg_attn, dim=1)

                print(f"     层{layer_idx + 1}最强注意力:")
                for word_idx, word in enumerate(words[:valid_length]):
                    target_idx = max_attn_pos[word_idx].item()
                    if target_idx < len(words):
                        target_word = words[target_idx]
                        attn_score = avg_attn[word_idx, target_idx].item()
                        print(f"       '{word}' → '{target_word}' ({attn_score:.3f})")

        print(f"\n🎯 编码器分析完成!")

    def _visualize_positional_encoding(self, pe_matrix, d_model, max_length):
        """可视化位置编码"""
        # 1. 位置编码热力图
        plt.figure(figsize=(15, 8))

        # 只显示前50个位置和前64个维度
        display_pos = min(50, max_length)
        display_dim = min(64, d_model)

        plt.subplot(2, 2, 1)
        sns.heatmap(pe_matrix[:display_pos, :display_dim].T,
                    cmap='RdYlBu', center=0, cbar=True)
        plt.title('位置编码热力图')
        plt.xlabel('位置')
        plt.ylabel('维度')

        # 2. 几个维度的位置编码曲线
        plt.subplot(2, 2, 2)
        for i in range(0, min(8, d_model), 2):
            plt.plot(pe_matrix[:display_pos, i], label=f'dim {i} (sin)', alpha=0.7)
            if i + 1 < d_model:
                plt.plot(pe_matrix[:display_pos, i + 1], label=f'dim {i + 1} (cos)', alpha=0.7)
        plt.title('位置编码曲线')
        plt.xlabel('位置')
        plt.ylabel('编码值')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 3. 不同位置的编码向量
        plt.subplot(2, 2, 3)
        positions_to_show = [0, 5, 10, 20]
        for pos in positions_to_show:
            if pos < max_length:
                plt.plot(pe_matrix[pos, :32], label=f'位置 {pos}', alpha=0.7)
        plt.title('不同位置的编码向量（前32维）')
        plt.xlabel('维度')
        plt.ylabel('编码值')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 4. 位置相似性矩阵
        plt.subplot(2, 2, 4)
        # 计算位置之间的余弦相似性
        pe_norm = pe_matrix[:display_pos] / np.linalg.norm(pe_matrix[:display_pos], axis=1, keepdims=True)
        similarity = np.dot(pe_norm, pe_norm.T)
        sns.heatmap(similarity, cmap='Blues', cbar=True)
        plt.title('位置间余弦相似性')
        plt.xlabel('位置')
        plt.ylabel('位置')

        plt.tight_layout()
        plt.savefig('tutorial_scripts/positional_encoding.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"   💾 位置编码可视化已保存: tutorial_scripts/positional_encoding.png")
        print("   👀 请查看弹出的图形窗口，关闭窗口后继续...")

    def _analyze_pe_properties(self, pe_matrix):
        """分析位置编码的性质"""
        print(f"\n🔬 位置编码性质分析:")

        # 1. 周期性分析
        print(f"   1. 周期性分析:")
        for dim in [0, 1, 10, 50]:
            if dim < pe_matrix.shape[1]:
                values = pe_matrix[:50, dim]
                # 简单的周期检测
                diffs = np.diff(values)
                sign_changes = np.sum(np.diff(np.sign(diffs)) != 0)
                print(f"      维度 {dim}: 符号变化次数 = {sign_changes}")

        # 2. 距离性质
        print(f"\n   2. 距离性质:")
        pos_pairs = [(0, 1), (0, 5), (0, 10), (5, 6)]
        for pos1, pos2 in pos_pairs:
            if pos2 < pe_matrix.shape[0]:
                dist = np.linalg.norm(pe_matrix[pos1] - pe_matrix[pos2])
                print(f"      位置 {pos1} 和 {pos2} 的欧氏距离: {dist:.4f}")

        # 3. 正交性
        print(f"\n   3. 正交性分析:")
        # 检查相邻维度的正交性
        for i in range(0, min(6, pe_matrix.shape[1] - 1), 2):
            dot_product = np.dot(pe_matrix[:, i], pe_matrix[:, i + 1])
            print(f"      维度 {i} 和 {i + 1} 的点积: {dot_product:.6f}")

    def _visualize_encoder_layer_flow(self, input_x, attn_out, res1, norm1, ff_out, res2, final_out):
        """可视化编码器层的数据流"""
        # 这里添加编码器层流程可视化的代码
        pass

    def _analyze_attention_patterns(self, attention_weights):
        """分析注意力模式"""
        # 这里添加注意力模式分析的代码
        pass

    def _visualize_encoder_layers(self, layer_outputs, layer_attentions):
        """可视化编码器各层"""
        # 这里添加编码器层级可视化的代码
        pass

    def _analyze_representation_learning(self, all_hidden, input_ids, attention_mask):
        """分析表示学习"""
        # 这里添加表示学习分析的代码
        pass


def main():
    """主函数：运行编码器教程"""
    print("🎓 欢迎来到Transformer编码器深度学习教程！")
    print("本教程将带你深入理解编码器的工作原理")

    visualizer = EncoderVisualizer()

    try:
        # 第1步：位置编码
        visualizer.step1_positional_encoding()

        input("\n按回车键继续到第2步...")

        # 第2步：编码器层
        visualizer.step2_encoder_layer()

        input("\n按回车键继续到第3步...")

        # 第3步：完整编码器
        visualizer.step3_complete_encoder()

        input("\n按回车键继续到第4步...")

        # 第4步：编码器分析
        visualizer.step4_encoder_analysis()

        print("\n🎉 编码器教程完成！")
        print("📁 所有可视化图片已保存在 tutorial_scripts/ 目录下")
        print("📚 接下来可以运行: python tutorial_scripts/step3_training.py")

    except KeyboardInterrupt:
        print("\n\n👋 教程被用户中断，再见！")


if __name__ == "__main__":
    main()
