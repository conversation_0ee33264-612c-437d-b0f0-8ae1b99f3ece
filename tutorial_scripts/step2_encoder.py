#!/usr/bin/env python3
"""
第二步：深度理解编码器结构
详细演示编码器的工作原理，包含位置编码、编码器层和完整编码器
"""

import torch
import numpy as np
import sys
from pathlib import Path

# 设置matplotlib为交互式显示
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体和样式

plt.style.use('default')

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
from src.utils.utils import setup_proxy, setup_chinese_font
from src.transformer.positional_encoding import PositionalEncoding
from src.transformer.encoder_layer import TransformerEncoderLayer
from src.transformer.encoder import TransformerEncoder


class EncoderVisualizer:
    """编码器可视化器"""

    def __init__(self, save_path):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.save_path = save_path
        print(f"🏗️ 编码器学习器初始化")
        print(f"   设备: {self.device}")

    def step1_positional_encoding(self):
        """第1步：位置编码详解"""
        print("\n" + "=" * 60)
        print("📚 第1步：位置编码原理与实现")
        print("=" * 60)

        # 参数设置
        d_model = 512
        max_length = 100

        print(f"📊 位置编码参数:")
        print(f"   模型维度: {d_model}")
        print(f"   最大长度: {max_length}")

        # 创建位置编码
        pe_layer = PositionalEncoding(d_model, max_length, dropout=0.0)

        print(f"\n⚙️  位置编码公式:")
        print(f"   PE(pos, 2i)   = sin(pos / 10000^(2i/d_model))")
        print(f"   PE(pos, 2i+1) = cos(pos / 10000^(2i/d_model))")
        print(f"   其中 pos 是位置，i 是维度索引")

        # 手动计算几个位置的编码来验证
        print(f"\n🔍 手动计算验证:")
        pos = 0  # 位置0
        for i in range(3):  # 前3个维度对
            dim_2i = 2 * i
            dim_2i_1 = 2 * i + 1

            # 计算分母项
            div_term = 10000 ** (2 * i / d_model)

            # 计算sin和cos
            sin_val = np.sin(pos / div_term)
            cos_val = np.cos(pos / div_term)

            print(f"   维度 {dim_2i:3d} (sin): {sin_val:.6f}")
            print(f"   维度 {dim_2i_1:3d} (cos): {cos_val:.6f}")

        # 获取位置编码矩阵
        pe_matrix = pe_layer.pe.squeeze(0).detach().numpy()  # [max_length, d_model]

        print(f"\n📈 位置编码矩阵形状: {pe_matrix.shape}")
        print(f"   位置0的前10个维度: {pe_matrix[0, :10]}")
        print(f"   位置1的前10个维度: {pe_matrix[1, :10]}")

        # 可视化位置编码
        self._visualize_positional_encoding(pe_matrix, d_model, max_length)

        # 分析位置编码的性质
        self._analyze_pe_properties(pe_matrix)

        return pe_layer

    def step2_encoder_layer(self):
        """第2步：编码器层详解"""
        print("\n" + "=" * 60)
        print("📚 第2步：编码器层结构与计算")
        print("=" * 60)

        # 参数设置
        batch_size = 2
        seq_length = 8
        d_model = 256
        n_heads = 8
        d_ff = 1024

        print(f"📊 编码器层参数:")
        print(f"   批次大小: {batch_size}")
        print(f"   序列长度: {seq_length}")
        print(f"   模型维度: {d_model}")
        print(f"   注意力头数: {n_heads}")
        print(f"   前馈网络维度: {d_ff}")

        # 创建编码器层
        encoder_layer = TransformerEncoderLayer(
            d_model=d_model,
            n_heads=n_heads,
            d_ff=d_ff,
            dropout=0.1,
            norm_first=False  # Post-LN结构
        ).to(self.device)

        # 创建输入
        x = torch.randn(batch_size, seq_length, d_model).to(self.device)

        print(f"\n🔍 输入形状: {x.shape}")
        print(f"   输入统计: 均值={x.mean().item():.4f}, 标准差={x.std().item():.4f}")

        # 详细跟踪编码器层的计算过程
        print(f"\n⚙️  编码器层计算过程 (Post-LN):")
        print(f"   结构: 输入 → 自注意力 → 残差+归一化 → 前馈网络 → 残差+归一化 → 输出")

        with torch.no_grad():
            # 保存原始输入
            input_original = x.clone()

            # 1. 自注意力
            print(f"\n   1️⃣ 自注意力计算:")
            attn_output, attn_weights = encoder_layer.self_attention(
                x, x, x, return_attention=True
            )
            print(f"      注意力输出: {attn_output.shape}")
            print(f"      注意力权重: {attn_weights.shape}")
            print(f"      输出统计: 均值={attn_output.mean().item():.4f}, 标准差={attn_output.std().item():.4f}")

            # 2. 残差连接 + 层归一化
            print(f"\n   2️⃣ 第一个残差连接和层归一化:")
            residual_1 = x + encoder_layer.dropout(attn_output)
            norm_1 = encoder_layer.norm1(residual_1)
            print(f"      残差连接后: 均值={residual_1.mean().item():.4f}, 标准差={residual_1.std().item():.4f}")
            print(f"      层归一化后: 均值={norm_1.mean().item():.4f}, 标准差={norm_1.std().item():.4f}")

            # 3. 前馈网络
            print(f"\n   3️⃣ 前馈网络:")
            ff_output = encoder_layer.feed_forward(norm_1)
            print(f"      前馈网络输出: {ff_output.shape}")
            print(f"      输出统计: 均值={ff_output.mean().item():.4f}, 标准差={ff_output.std().item():.4f}")

            # 4. 第二个残差连接 + 层归一化
            print(f"\n   4️⃣ 第二个残差连接和层归一化:")
            residual_2 = norm_1 + encoder_layer.dropout(ff_output)
            final_output = encoder_layer.norm2(residual_2)
            print(f"      残差连接后: 均值={residual_2.mean().item():.4f}, 标准差={residual_2.std().item():.4f}")
            print(f"      最终输出: 均值={final_output.mean().item():.4f}, 标准差={final_output.std().item():.4f}")

        # 使用官方接口验证
        official_output, official_attn = encoder_layer(x, return_attention=True)

        print(f"\n✅ 验证结果:")
        print(f"   手动计算输出: {final_output.shape}")
        print(f"   官方接口输出: {official_output.shape}")
        print(f"   输出差异: {torch.max(torch.abs(final_output - official_output)).item():.6f}")

        # 可视化编码器层的变换过程
        self._visualize_encoder_layer_flow(
            input_original, attn_output, residual_1, norm_1,
            ff_output, residual_2, final_output
        )

        # 分析注意力模式
        self._analyze_attention_patterns(official_attn)

        return encoder_layer, official_output

    def step3_complete_encoder(self):
        """第3步：完整编码器"""
        print("\n" + "=" * 60)
        print("📚 第3步：完整编码器结构")
        print("=" * 60)

        # 参数设置
        vocab_size = 1000
        batch_size = 2
        seq_length = 10
        d_model = 256
        n_heads = 8
        n_layers = 4
        d_ff = 1024

        print(f"📊 完整编码器参数:")
        print(f"   词汇表大小: {vocab_size}")
        print(f"   模型维度: {d_model}")
        print(f"   层数: {n_layers}")
        print(f"   注意力头数: {n_heads}")

        # 创建完整编码器
        encoder = TransformerEncoder(
            vocab_size=vocab_size,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            d_ff=d_ff,
            dropout=0.1
        ).to(self.device)

        # 创建输入序列
        input_ids = torch.randint(1, vocab_size, (batch_size, seq_length)).to(self.device)
        attention_mask = torch.ones_like(input_ids).to(self.device)
        attention_mask[:, 7:] = 0  # 模拟padding

        print(f"\n🔍 输入数据:")
        print(f"   输入ID: {input_ids.shape}")
        print(f"   注意力掩码: {attention_mask.shape}")
        print(f"   第一个样本: {input_ids[0].cpu().numpy()}")
        print(f"   第一个掩码: {attention_mask[0].cpu().numpy()}")

        # 详细跟踪编码器的计算过程
        print(f"\n⚙️  完整编码器计算过程:")

        with torch.no_grad():
            # 1. Token嵌入
            embeddings = encoder.token_embedding(input_ids) * encoder.embed_scale
            print(f"\n   1️⃣ Token嵌入:")
            print(f"      嵌入形状: {embeddings.shape}")
            print(f"      缩放因子: {encoder.embed_scale:.4f}")
            print(f"      嵌入统计: 均值={embeddings.mean().item():.4f}, 标准差={embeddings.std().item():.4f}")

            # 2. 位置编码
            pos_encoded = encoder.positional_encoding(embeddings)
            print(f"\n   2️⃣ 位置编码:")
            print(f"      位置编码后: {pos_encoded.shape}")
            print(f"      统计: 均值={pos_encoded.mean().item():.4f}, 标准差={pos_encoded.std().item():.4f}")

            # 3. 通过各个编码器层
            x = pos_encoded
            layer_outputs = [x]
            layer_attentions = []

            # 创建扩展的注意力掩码
            extended_mask = encoder._create_extended_attention_mask(attention_mask)

            for i, layer in enumerate(encoder.layers):
                print(f"\n   3️⃣ 编码器层 {i + 1}:")
                x, attn = layer(x, mask=extended_mask, return_attention=True)
                layer_outputs.append(x)
                layer_attentions.append(attn)
                print(f"      层{i + 1}输出: 均值={x.mean().item():.4f}, 标准差={x.std().item():.4f}")

            # 4. 最终层归一化
            if encoder.final_norm is not None:
                final_output = encoder.final_norm(x)
                print(f"\n   4️⃣ 最终层归一化:")
                print(f"      最终输出: 均值={final_output.mean().item():.4f}, 标准差={final_output.std().item():.4f}")
            else:
                final_output = x

        # 使用官方接口验证
        official_output, all_hidden, all_attention = encoder(
            input_ids, attention_mask=attention_mask,
            return_all_hidden_states=True, return_attention_weights=True
        )

        print(f"\n✅ 验证结果:")
        print(f"   手动计算输出: {final_output.shape}")
        print(f"   官方接口输出: {official_output.shape}")
        print(f"   隐藏状态层数: {len(all_hidden)}")
        print(f"   注意力权重层数: {len(all_attention)}")
        print(f"   输出差异: {torch.max(torch.abs(final_output - official_output)).item():.6f}")

        # 可视化编码器的层级变化
        self._visualize_encoder_layers(layer_outputs, layer_attentions)

        # 分析编码器的表示学习
        self._analyze_representation_learning(all_hidden, input_ids, attention_mask)

        return encoder, official_output

    def step4_encoder_analysis(self):
        """第4步：编码器深度分析"""
        print("\n" + "=" * 60)
        print("📚 第4步：编码器深度分析")
        print("=" * 60)

        # 创建有意义的输入来分析
        sentences = [
            "The quick brown fox jumps over the lazy dog",
            "I love natural language processing and machine learning",
            "Transformer models are very powerful for NLP tasks"
        ]

        # 简单的分词和词汇表构建
        all_words = []
        for sentence in sentences:
            all_words.extend(sentence.lower().split())

        vocab = {'<pad>': 0, '<unk>': 1}
        for word in set(all_words):
            vocab[word] = len(vocab)

        print(f"📝 构建词汇表: {len(vocab)} 个词")

        # 转换句子
        max_length = 12
        sequences = []
        for sentence in sentences:
            words = sentence.lower().split()
            ids = [vocab.get(word, vocab['<unk>']) for word in words]
            if len(ids) < max_length:
                ids.extend([vocab['<pad>']] * (max_length - len(ids)))
            else:
                ids = ids[:max_length]
            sequences.append(ids)

        # 创建编码器
        encoder = TransformerEncoder(
            vocab_size=len(vocab),
            d_model=128,
            n_heads=4,
            n_layers=3,
            d_ff=512,
            dropout=0.0
        ).to(self.device)

        print(f"\n🔍 分析句子编码:")
        for i, (sentence, seq) in enumerate(zip(sentences, sequences)):
            print(f"\n   句子 {i + 1}: '{sentence}'")

            # 编码
            input_ids = torch.tensor([seq]).to(self.device)
            attention_mask = torch.tensor([[1 if id != vocab['<pad>'] else 0 for id in seq]]).to(self.device)

            output, all_hidden, all_attention = encoder(
                input_ids, attention_mask=attention_mask,
                return_all_hidden_states=True, return_attention_weights=True
            )

            # 分析每层的表示
            print(f"   各层表示变化:")
            for layer_idx, hidden in enumerate(all_hidden):
                layer_mean = hidden.mean().item()
                layer_std = hidden.std().item()
                print(f"     层{layer_idx}: 均值={layer_mean:.4f}, 标准差={layer_std:.4f}")

            # 分析注意力模式
            print(f"   注意力模式分析:")
            words = sentence.lower().split()
            valid_length = min(len(words), max_length)

            for layer_idx, attn in enumerate(all_attention):
                avg_attn = attn.mean(dim=1).squeeze(0)[:valid_length, :valid_length]
                max_attn_pos = torch.argmax(avg_attn, dim=1)

                print(f"     层{layer_idx + 1}最强注意力:")
                for word_idx, word in enumerate(words[:valid_length]):
                    target_idx = max_attn_pos[word_idx].item()
                    if target_idx < len(words):
                        target_word = words[target_idx]
                        attn_score = avg_attn[word_idx, target_idx].item()
                        print(f"       '{word}' → '{target_word}' ({attn_score:.3f})")

        print(f"\n🎯 编码器分析完成!")

    def _visualize_positional_encoding(self, pe_matrix, d_model, max_length):
        """可视化位置编码"""
        # 1. 位置编码热力图
        plt.figure(figsize=(15, 8))

        # 只显示前50个位置和前64个维度
        display_pos = min(50, max_length)
        display_dim = min(64, d_model)

        plt.subplot(2, 2, 1)
        sns.heatmap(pe_matrix[:display_pos, :display_dim].T,
                    cmap='RdYlBu', center=0, cbar=True)
        plt.title('位置编码热力图')
        plt.xlabel('位置')
        plt.ylabel('维度')

        # 2. 几个维度的位置编码曲线
        plt.subplot(2, 2, 2)
        for i in range(0, min(8, d_model), 2):
            plt.plot(pe_matrix[:display_pos, i], label=f'dim {i} (sin)', alpha=0.7)
            if i + 1 < d_model:
                plt.plot(pe_matrix[:display_pos, i + 1], label=f'dim {i + 1} (cos)', alpha=0.7)
        plt.title('位置编码曲线')
        plt.xlabel('位置')
        plt.ylabel('编码值')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 3. 不同位置的编码向量
        plt.subplot(2, 2, 3)
        positions_to_show = [0, 5, 10, 20]
        for pos in positions_to_show:
            if pos < max_length:
                plt.plot(pe_matrix[pos, :32], label=f'位置 {pos}', alpha=0.7)
        plt.title('不同位置的编码向量（前32维）')
        plt.xlabel('维度')
        plt.ylabel('编码值')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 4. 位置相似性矩阵
        plt.subplot(2, 2, 4)
        # 计算位置之间的余弦相似性
        pe_norm = pe_matrix[:display_pos] / np.linalg.norm(pe_matrix[:display_pos], axis=1, keepdims=True)
        similarity = np.dot(pe_norm, pe_norm.T)
        sns.heatmap(similarity, cmap='Blues', cbar=True)
        plt.title('位置间余弦相似性')
        plt.xlabel('位置')
        plt.ylabel('位置')

        plt.tight_layout()
        plt.savefig(f'{self.save_path}/positional_encoding.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"   💾 位置编码可视化已保存: tutorial_scripts/positional_encoding.png")
        print("   👀 请查看弹出的图形窗口，关闭窗口后继续...")

    def _analyze_pe_properties(self, pe_matrix):
        """分析位置编码的性质"""
        print(f"\n🔬 位置编码性质分析:")

        # 1. 周期性分析
        print(f"   1. 周期性分析:")
        for dim in [0, 1, 10, 50]:
            if dim < pe_matrix.shape[1]:
                values = pe_matrix[:50, dim]
                # 简单的周期检测
                diffs = np.diff(values)
                sign_changes = np.sum(np.diff(np.sign(diffs)) != 0)
                print(f"      维度 {dim}: 符号变化次数 = {sign_changes}")

        # 2. 距离性质
        print(f"\n   2. 距离性质:")
        pos_pairs = [(0, 1), (0, 5), (0, 10), (5, 6)]
        for pos1, pos2 in pos_pairs:
            if pos2 < pe_matrix.shape[0]:
                dist = np.linalg.norm(pe_matrix[pos1] - pe_matrix[pos2])
                print(f"      位置 {pos1} 和 {pos2} 的欧氏距离: {dist:.4f}")

        # 3. 正交性
        print(f"\n   3. 正交性分析:")
        # 检查相邻维度的正交性
        for i in range(0, min(6, pe_matrix.shape[1] - 1), 2):
            dot_product = np.dot(pe_matrix[:, i], pe_matrix[:, i + 1])
            print(f"      维度 {i} 和 {i + 1} 的点积: {dot_product:.6f}")

    def _visualize_encoder_layer_flow(self, input_x, attn_out, res1, norm1, ff_out, res2, final_out):
        """可视化编码器层的数据流"""
        print(f"\n📊 可视化编码器层数据流...")

        # 将所有张量移到CPU并转换为numpy
        tensors = [input_x, attn_out, res1, norm1, ff_out, res2, final_out]
        tensor_names = ['输入', '注意力输出', '残差1', '归一化1', '前馈输出', '残差2', '最终输出']

        # 计算统计信息
        stats = []
        for tensor in tensors:
            tensor_cpu = tensor.detach().cpu().numpy()
            stats.append({
                'mean': np.mean(tensor_cpu),
                'std': np.std(tensor_cpu),
                'min': np.min(tensor_cpu),
                'max': np.max(tensor_cpu)
            })

        # 创建可视化
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('编码器层数据流可视化', fontsize=16, fontweight='bold')

        # 1. 统计信息变化
        ax1 = axes[0, 0]
        means = [s['mean'] for s in stats]
        stds = [s['std'] for s in stats]
        x_pos = range(len(tensor_names))

        ax1.plot(x_pos, means, 'o-', label='均值', linewidth=2, markersize=8)
        ax1.plot(x_pos, stds, 's-', label='标准差', linewidth=2, markersize=8)
        ax1.set_xticks(x_pos)
        ax1.set_xticklabels(tensor_names, rotation=45, ha='right')
        ax1.set_title('统计信息变化')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 数值范围变化
        ax2 = axes[0, 1]
        mins = [s['min'] for s in stats]
        maxs = [s['max'] for s in stats]

        ax2.fill_between(x_pos, mins, maxs, alpha=0.3, label='数值范围')
        ax2.plot(x_pos, mins, 'v-', label='最小值', linewidth=2)
        ax2.plot(x_pos, maxs, '^-', label='最大值', linewidth=2)
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(tensor_names, rotation=45, ha='right')
        ax2.set_title('数值范围变化')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 第一个token的特征变化
        ax3 = axes[0, 2]
        first_token_features = []
        for tensor in tensors:
            tensor_cpu = tensor.detach().cpu().numpy()
            # 取第一个样本的第一个token的前16个特征
            features = tensor_cpu[0, 0, :16]
            first_token_features.append(features)

        # 创建热力图
        feature_matrix = np.array(first_token_features)
        im = ax3.imshow(feature_matrix, cmap='RdYlBu', aspect='auto')
        ax3.set_yticks(range(len(tensor_names)))
        ax3.set_yticklabels(tensor_names)
        ax3.set_xlabel('特征维度')
        ax3.set_title('第一个Token特征变化')
        plt.colorbar(im, ax=ax3)

        # 4. 残差连接效果
        ax4 = axes[1, 0]
        input_norm = torch.norm(input_x, dim=-1).detach().cpu().numpy()
        res1_norm = torch.norm(res1, dim=-1).detach().cpu().numpy()
        res2_norm = torch.norm(res2, dim=-1).detach().cpu().numpy()

        ax4.plot(input_norm[0], 'o-', label='输入范数', alpha=0.7)
        ax4.plot(res1_norm[0], 's-', label='第一次残差后', alpha=0.7)
        ax4.plot(res2_norm[0], '^-', label='第二次残差后', alpha=0.7)
        ax4.set_xlabel('序列位置')
        ax4.set_ylabel('向量范数')
        ax4.set_title('残差连接效果')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # 5. 层归一化效果
        ax5 = axes[1, 1]
        # 比较归一化前后的分布
        res1_flat = res1.detach().cpu().numpy().flatten()
        norm1_flat = norm1.detach().cpu().numpy().flatten()

        ax5.hist(res1_flat, bins=50, alpha=0.5, label='归一化前', density=True)
        ax5.hist(norm1_flat, bins=50, alpha=0.5, label='归一化后', density=True)
        ax5.set_xlabel('数值')
        ax5.set_ylabel('密度')
        ax5.set_title('层归一化效果')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

        # 6. 变换强度
        ax6 = axes[1, 2]
        # 计算每步的变化量
        changes = []
        change_names = []

        # 注意力变化
        attn_change = torch.norm(attn_out - input_x, dim=-1).mean().item()
        changes.append(attn_change)
        change_names.append('注意力变化')

        # 第一次残差+归一化变化
        norm1_change = torch.norm(norm1 - res1, dim=-1).mean().item()
        changes.append(norm1_change)
        change_names.append('第一次归一化')

        # 前馈网络变化
        ff_change = torch.norm(ff_out - norm1, dim=-1).mean().item()
        changes.append(ff_change)
        change_names.append('前馈网络变化')

        # 第二次残差+归一化变化
        final_change = torch.norm(final_out - res2, dim=-1).mean().item()
        changes.append(final_change)
        change_names.append('第二次归一化')

        bars = ax6.bar(change_names, changes, color=['skyblue', 'lightgreen', 'orange', 'pink'])
        ax6.set_ylabel('平均变化量')
        ax6.set_title('各步骤变换强度')
        ax6.tick_params(axis='x', rotation=45)

        # 添加数值标签
        for bar, change in zip(bars, changes):
            height = bar.get_height()
            ax6.text(bar.get_x() + bar.get_width() / 2., height,
                     f'{change:.3f}', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig(f'{self.save_path}/encoder_layer_flow.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"   💾 编码器层流程可视化已保存: {self.save_path}/encoder_layer_flow.png")

    def _analyze_attention_patterns(self, attention_weights):
        """分析注意力模式"""
        print(f"\n🔍 分析注意力模式...")

        # attention_weights: [batch_size, n_heads, seq_len, seq_len]
        batch_size, n_heads, seq_len, _ = attention_weights.shape
        attn_cpu = attention_weights.detach().cpu().numpy()

        # 创建可视化
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('注意力模式分析', fontsize=16, fontweight='bold')

        # 1. 平均注意力热力图
        ax1 = axes[0, 0]
        avg_attn = np.mean(attn_cpu[0], axis=0)  # 平均所有头
        im1 = ax1.imshow(avg_attn, cmap='Blues', interpolation='nearest')
        ax1.set_title('平均注意力热力图')
        ax1.set_xlabel('Key位置')
        ax1.set_ylabel('Query位置')
        plt.colorbar(im1, ax=ax1)

        # 添加网格
        ax1.set_xticks(range(seq_len))
        ax1.set_yticks(range(seq_len))
        ax1.grid(True, alpha=0.3)

        # 2. 不同头的注意力模式
        ax2 = axes[0, 1]
        head_to_show = min(4, n_heads)
        for head in range(head_to_show):
            # 计算每个头的对角线注意力强度
            diag_strength = np.diag(attn_cpu[0, head])
            ax2.plot(diag_strength, label=f'头 {head}', marker='o', alpha=0.7)

        ax2.set_title('各头对角线注意力强度')
        ax2.set_xlabel('位置')
        ax2.set_ylabel('自注意力强度')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 注意力分布统计
        ax3 = axes[0, 2]
        # 计算每个位置的注意力熵
        entropies = []
        for i in range(seq_len):
            for head in range(n_heads):
                attn_dist = attn_cpu[0, head, i, :]
                # 避免log(0)
                attn_dist = attn_dist + 1e-10
                entropy = -np.sum(attn_dist * np.log(attn_dist))
                entropies.append(entropy)

        ax3.hist(entropies, bins=20, alpha=0.7, edgecolor='black')
        ax3.set_title('注意力分布熵')
        ax3.set_xlabel('熵值')
        ax3.set_ylabel('频次')
        ax3.grid(True, alpha=0.3)

        # 4. 头间相似性
        ax4 = axes[1, 0]
        if n_heads > 1:
            head_similarities = np.zeros((n_heads, n_heads))
            for i in range(n_heads):
                for j in range(n_heads):
                    # 计算两个头的注意力模式相似性
                    attn_i = attn_cpu[0, i].flatten()
                    attn_j = attn_cpu[0, j].flatten()
                    similarity = np.corrcoef(attn_i, attn_j)[0, 1]
                    head_similarities[i, j] = similarity

            im4 = ax4.imshow(head_similarities, cmap='RdYlBu', vmin=-1, vmax=1)
            ax4.set_title('注意力头间相似性')
            ax4.set_xlabel('头索引')
            ax4.set_ylabel('头索引')
            plt.colorbar(im4, ax=ax4)

            # 添加数值标签
            for i in range(n_heads):
                for j in range(n_heads):
                    text = ax4.text(j, i, f'{head_similarities[i, j]:.2f}',
                                    ha="center", va="center", color="black", fontsize=8)
        else:
            ax4.text(0.5, 0.5, '只有一个注意力头', ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('注意力头间相似性')

        # 5. 位置偏好分析
        ax5 = axes[1, 1]
        # 计算每个位置作为key被关注的总强度
        key_attention = np.sum(avg_attn, axis=0)
        # 计算每个位置作为query关注其他位置的总强度
        query_attention = np.sum(avg_attn, axis=1)

        x_pos = range(seq_len)
        width = 0.35

        bars1 = ax5.bar([x - width / 2 for x in x_pos], key_attention, width,
                        label='作为Key被关注', alpha=0.7)
        bars2 = ax5.bar([x + width / 2 for x in x_pos], query_attention, width,
                        label='作为Query关注他人', alpha=0.7)

        ax5.set_title('位置注意力偏好')
        ax5.set_xlabel('位置')
        ax5.set_ylabel('注意力强度')
        ax5.set_xticks(x_pos)
        ax5.legend()
        ax5.grid(True, alpha=0.3)

        # 6. 注意力距离分析
        ax6 = axes[1, 2]
        distances = []
        weights = []

        for i in range(seq_len):
            for j in range(seq_len):
                distance = abs(i - j)
                weight = avg_attn[i, j]
                distances.append(distance)
                weights.append(weight)

        # 按距离分组计算平均注意力
        max_dist = max(distances)
        dist_avg_attn = []
        for d in range(max_dist + 1):
            dist_weights = [w for dist, w in zip(distances, weights) if dist == d]
            if dist_weights:
                dist_avg_attn.append(np.mean(dist_weights))
            else:
                dist_avg_attn.append(0)

        ax6.plot(range(max_dist + 1), dist_avg_attn, 'o-', linewidth=2, markersize=6)
        ax6.set_title('注意力vs位置距离')
        ax6.set_xlabel('位置距离')
        ax6.set_ylabel('平均注意力强度')
        ax6.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{self.save_path}/attention_patterns.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"   💾 注意力模式分析已保存: {self.save_path}/attention_patterns.png")

        # 打印数值分析结果
        print(f"\n📊 注意力模式数值分析:")
        print(f"   平均注意力熵: {np.mean(entropies):.4f}")
        print(f"   对角线注意力强度: {np.mean(np.diag(avg_attn)):.4f}")
        print(f"   最大注意力权重: {np.max(avg_attn):.4f}")
        print(f"   注意力权重标准差: {np.std(avg_attn):.4f}")

        # 分析注意力模式类型
        diag_strength = np.mean(np.diag(avg_attn))
        off_diag_strength = np.mean(avg_attn - np.diag(np.diag(avg_attn)))

        if diag_strength > off_diag_strength * 2:
            pattern_type = "自注意力主导型"
        elif off_diag_strength > diag_strength * 1.5:
            pattern_type = "交互注意力主导型"
        else:
            pattern_type = "平衡型"

        print(f"   注意力模式类型: {pattern_type}")
        print(f"   自注意力强度: {diag_strength:.4f}")
        print(f"   交互注意力强度: {off_diag_strength:.4f}")

    def _visualize_encoder_layers(self, layer_outputs, layer_attentions):
        """可视化编码器各层"""
        print(f"\n📊 可视化编码器各层变化...")

        n_layers = len(layer_outputs) - 1  # 减去初始输入

        # 创建可视化
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('编码器各层变化分析', fontsize=16, fontweight='bold')

        # 1. 各层表示的统计变化
        ax1 = axes[0, 0]
        layer_means = []
        layer_stds = []
        layer_norms = []

        for i, output in enumerate(layer_outputs):
            output_cpu = output.detach().cpu().numpy()
            layer_means.append(np.mean(output_cpu))
            layer_stds.append(np.std(output_cpu))
            layer_norms.append(np.mean(np.linalg.norm(output_cpu, axis=-1)))

        x_layers = range(len(layer_outputs))
        ax1.plot(x_layers, layer_means, 'o-', label='均值', linewidth=2, markersize=8)
        ax1.plot(x_layers, layer_stds, 's-', label='标准差', linewidth=2, markersize=8)
        ax1.plot(x_layers, layer_norms, '^-', label='平均范数', linewidth=2, markersize=8)

        ax1.set_xlabel('层数 (0=输入)')
        ax1.set_ylabel('统计值')
        ax1.set_title('各层表示统计变化')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_xticks(x_layers)

        # 2. 层间相似性
        ax2 = axes[0, 1]
        if len(layer_outputs) > 1:
            n_compare = len(layer_outputs)
            similarity_matrix = np.zeros((n_compare, n_compare))

            for i in range(n_compare):
                for j in range(n_compare):
                    # 计算两层输出的余弦相似性
                    out_i = layer_outputs[i].detach().cpu().numpy().flatten()
                    out_j = layer_outputs[j].detach().cpu().numpy().flatten()

                    # 归一化
                    norm_i = np.linalg.norm(out_i)
                    norm_j = np.linalg.norm(out_j)

                    if norm_i > 0 and norm_j > 0:
                        similarity = np.dot(out_i, out_j) / (norm_i * norm_j)
                    else:
                        similarity = 0

                    similarity_matrix[i, j] = similarity

            im2 = ax2.imshow(similarity_matrix, cmap='Blues', vmin=0, vmax=1)
            ax2.set_title('层间表示相似性')
            ax2.set_xlabel('层数')
            ax2.set_ylabel('层数')
            plt.colorbar(im2, ax=ax2)

            # 添加数值标签
            for i in range(n_compare):
                for j in range(n_compare):
                    text = ax2.text(j, i, f'{similarity_matrix[i, j]:.2f}',
                                    ha="center", va="center",
                                    color="white" if similarity_matrix[i, j] > 0.5 else "black",
                                    fontsize=8)

        # 3. 特征变化热力图
        ax3 = axes[0, 2]
        # 选择第一个样本的第一个token，显示特征在各层的变化
        feature_changes = []
        for output in layer_outputs:
            # 取前32个特征维度
            features = output[0, 0, :32].detach().cpu().numpy()
            feature_changes.append(features)

        feature_matrix = np.array(feature_changes)
        im3 = ax3.imshow(feature_matrix, cmap='RdYlBu', aspect='auto')
        ax3.set_title('特征在各层的变化')
        ax3.set_xlabel('特征维度')
        ax3.set_ylabel('层数')
        ax3.set_yticks(range(len(layer_outputs)))
        ax3.set_yticklabels([f'层{i}' if i > 0 else '输入' for i in range(len(layer_outputs))])
        plt.colorbar(im3, ax=ax3)

        # 4. 注意力模式演化
        ax4 = axes[1, 0]
        layer_entropies = None
        if layer_attentions:
            # 计算每层的平均注意力熵
            layer_entropies = []
            layer_self_attn = []

            for attn in layer_attentions:
                attn_cpu = attn.detach().cpu().numpy()
                batch_size, n_heads, seq_len, _ = attn_cpu.shape

                # 计算平均注意力
                avg_attn = np.mean(attn_cpu[0], axis=0)

                # 计算熵
                entropies = []
                for i in range(seq_len):
                    attn_dist = avg_attn[i, :] + 1e-10
                    entropy = -np.sum(attn_dist * np.log(attn_dist))
                    entropies.append(entropy)

                layer_entropies.append(np.mean(entropies))

                # 计算自注意力强度
                self_attn = np.mean(np.diag(avg_attn))
                layer_self_attn.append(self_attn)

            x_attn_layers = range(1, len(layer_attentions) + 1)
            ax4.plot(x_attn_layers, layer_entropies, 'o-', label='平均注意力熵', linewidth=2, markersize=8)
            ax4.plot(x_attn_layers, layer_self_attn, 's-', label='自注意力强度', linewidth=2, markersize=8)

            ax4.set_xlabel('编码器层')
            ax4.set_ylabel('注意力指标')
            ax4.set_title('注意力模式演化')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
            ax4.set_xticks(x_attn_layers)
        else:
            ax4.text(0.5, 0.5, '无注意力权重数据', ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('注意力模式演化')

        # 5. 层间变化量
        ax5 = axes[1, 1]
        layer_changes = None
        if len(layer_outputs) > 1:
            layer_changes = []
            for i in range(1, len(layer_outputs)):
                prev_output = layer_outputs[i - 1].detach().cpu().numpy()
                curr_output = layer_outputs[i].detach().cpu().numpy()

                # 计算变化量
                change = np.mean(np.linalg.norm(curr_output - prev_output, axis=-1))
                layer_changes.append(change)

            x_change_layers = range(1, len(layer_outputs))
            bars = ax5.bar(x_change_layers, layer_changes, alpha=0.7, color='skyblue')

            ax5.set_xlabel('编码器层')
            ax5.set_ylabel('平均变化量')
            ax5.set_title('各层表示变化量')
            ax5.set_xticks(x_change_layers)
            ax5.grid(True, alpha=0.3)

            # 添加数值标签
            for bar, change in zip(bars, layer_changes):
                height = bar.get_height()
                ax5.text(bar.get_x() + bar.get_width() / 2., height,
                         f'{change:.3f}', ha='center', va='bottom')

        # 6. 表示空间分析
        ax6 = axes[1, 2]
        # 使用PCA降维可视化表示空间
        from sklearn.decomposition import PCA

        # 选择几个关键层进行比较
        layers_to_compare = [0, len(layer_outputs) // 2, len(layer_outputs) - 1]
        colors = ['red', 'green', 'blue']
        labels = ['输入', f'中间层{len(layer_outputs) // 2}', f'最终层{len(layer_outputs) - 1}']

        for idx, (layer_idx, color, label) in enumerate(zip(layers_to_compare, colors, labels)):
            if layer_idx < len(layer_outputs):
                # 取第一个样本的所有token
                output = layer_outputs[layer_idx][0].detach().cpu().numpy()  # [seq_len, d_model]

                if output.shape[1] >= 2:  # 确保有足够的维度进行PCA
                    if output.shape[1] > 2:
                        pca = PCA(n_components=2)
                        output_2d = pca.fit_transform(output)
                    else:
                        output_2d = output

                    ax6.scatter(output_2d[:, 0], output_2d[:, 1],
                                c=color, label=label, alpha=0.7, s=50)

        ax6.set_xlabel('PCA维度1')
        ax6.set_ylabel('PCA维度2')
        ax6.set_title('表示空间演化 (PCA)')
        ax6.legend()
        ax6.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{self.save_path}/encoder_layers.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"   💾 编码器层级可视化已保存: {self.save_path}/encoder_layers.png")

        # 打印数值分析
        print(f"\n📊 编码器层级数值分析:")
        print(f"   总层数: {n_layers}")
        print(f"   输入表示范数: {layer_norms[0]:.4f}")
        print(f"   最终表示范数: {layer_norms[-1]:.4f}")
        if len(layer_changes) > 0:
            print(f"   平均层间变化: {np.mean(layer_changes):.4f}")
            print(f"   最大层间变化: {np.max(layer_changes):.4f} (层{np.argmax(layer_changes) + 1})")
        if layer_entropies:
            print(f"   注意力熵变化: {layer_entropies[0]:.4f} → {layer_entropies[-1]:.4f}")

    def _analyze_representation_learning(self, all_hidden, input_ids, attention_mask):
        """分析表示学习"""
        print(f"\n🧠 分析表示学习过程...")

        n_layers = len(all_hidden)
        batch_size, seq_len, d_model = all_hidden[0].shape

        # 创建可视化
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('表示学习分析', fontsize=16, fontweight='bold')

        # 1. 表示质量演化
        ax1 = axes[0, 0]

        # 计算各层的表示质量指标
        layer_qualities = []
        layer_separabilities = []

        for layer_idx, hidden in enumerate(all_hidden):
            hidden_cpu = hidden.detach().cpu().numpy()

            # 表示质量：计算有效维度（基于奇异值分解）
            # 取第一个样本
            sample_hidden = hidden_cpu[0]  # [seq_len, d_model]

            # 只考虑非padding的token
            valid_mask = attention_mask[0].cpu().numpy().astype(bool)
            valid_hidden = sample_hidden[valid_mask]

            if valid_hidden.shape[0] > 1:
                # SVD分析
                U, S, Vt = np.linalg.svd(valid_hidden, full_matrices=False)

                # 计算有效维度（奇异值的有效秩）
                total_variance = np.sum(S ** 2)
                cumsum_variance = np.cumsum(S ** 2)
                effective_rank = np.sum(cumsum_variance / total_variance < 0.95) + 1

                layer_qualities.append(effective_rank / len(S))  # 归一化

                # 可分离性：计算token间的平均距离
                distances = []
                for i in range(len(valid_hidden)):
                    for j in range(i + 1, len(valid_hidden)):
                        dist = np.linalg.norm(valid_hidden[i] - valid_hidden[j])
                        distances.append(dist)

                layer_separabilities.append(np.mean(distances) if distances else 0)
            else:
                layer_qualities.append(0)
                layer_separabilities.append(0)

        x_layers = range(n_layers)
        ax1.plot(x_layers, layer_qualities, 'o-', label='表示质量', linewidth=2, markersize=8)
        ax1.plot(x_layers, np.array(layer_separabilities) / np.max(layer_separabilities) if np.max(
            layer_separabilities) > 0 else layer_separabilities,
                 's-', label='可分离性(归一化)', linewidth=2, markersize=8)

        ax1.set_xlabel('层数')
        ax1.set_ylabel('质量指标')
        ax1.set_title('表示质量演化')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_xticks(x_layers)

        # 2. 特征激活模式
        ax2 = axes[0, 1]

        # 计算各层的激活统计
        activation_stats = []
        for hidden in all_hidden:
            hidden_cpu = hidden.detach().cpu().numpy()

            # 计算激活的稀疏性
            abs_activations = np.abs(hidden_cpu)
            threshold = np.percentile(abs_activations, 90)  # 90%分位数作为阈值
            active_ratio = np.mean(abs_activations > threshold)

            activation_stats.append({
                'sparsity': 1 - active_ratio,
                'mean_activation': np.mean(abs_activations),
                'max_activation': np.max(abs_activations)
            })

        sparsities = [stat['sparsity'] for stat in activation_stats]
        mean_activations = [stat['mean_activation'] for stat in activation_stats]

        ax2.plot(x_layers, sparsities, 'o-', label='稀疏性', linewidth=2, markersize=8)
        ax2_twin = ax2.twinx()
        ax2_twin.plot(x_layers, mean_activations, 's-', color='orange', label='平均激活', linewidth=2, markersize=8)

        ax2.set_xlabel('层数')
        ax2.set_ylabel('稀疏性', color='blue')
        ax2_twin.set_ylabel('平均激活强度', color='orange')
        ax2.set_title('激活模式分析')
        ax2.grid(True, alpha=0.3)
        ax2.set_xticks(x_layers)

        # 添加图例
        lines1, labels1 = ax2.get_legend_handles_labels()
        lines2, labels2 = ax2_twin.get_legend_handles_labels()
        ax2.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

        # 3. 位置编码效应
        ax3 = axes[0, 2]

        # 分析位置信息在各层的保持程度
        position_effects = []

        for hidden in all_hidden:
            hidden_cpu = hidden[0].detach().cpu().numpy()  # 第一个样本
            valid_mask = attention_mask[0].cpu().numpy().astype(bool)
            valid_hidden = hidden_cpu[valid_mask]

            if len(valid_hidden) > 2:
                # 计算相邻位置的相似性
                adjacent_similarities = []
                for i in range(len(valid_hidden) - 1):
                    sim = np.dot(valid_hidden[i], valid_hidden[i + 1]) / (
                            np.linalg.norm(valid_hidden[i]) * np.linalg.norm(valid_hidden[i + 1]) + 1e-8)
                    adjacent_similarities.append(sim)

                # 计算远距离位置的相似性
                distant_similarities = []
                for i in range(len(valid_hidden)):
                    for j in range(i + 2, len(valid_hidden)):
                        sim = np.dot(valid_hidden[i], valid_hidden[j]) / (
                                np.linalg.norm(valid_hidden[i]) * np.linalg.norm(valid_hidden[j]) + 1e-8)
                        distant_similarities.append(sim)

                position_effects.append({
                    'adjacent_sim': np.mean(adjacent_similarities) if adjacent_similarities else 0,
                    'distant_sim': np.mean(distant_similarities) if distant_similarities else 0
                })
            else:
                position_effects.append({'adjacent_sim': 0, 'distant_sim': 0})

        adjacent_sims = [pe['adjacent_sim'] for pe in position_effects]
        distant_sims = [pe['distant_sim'] for pe in position_effects]

        ax3.plot(x_layers, adjacent_sims, 'o-', label='相邻位置相似性', linewidth=2, markersize=8)
        ax3.plot(x_layers, distant_sims, 's-', label='远距离位置相似性', linewidth=2, markersize=8)

        ax3.set_xlabel('层数')
        ax3.set_ylabel('相似性')
        ax3.set_title('位置编码效应')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_xticks(x_layers)

        # 4. 表示聚类分析
        ax4 = axes[1, 0]

        # 使用t-SNE可视化最后一层的表示
        from sklearn.manifold import TSNE

        final_hidden = all_hidden[-1][0].detach().cpu().numpy()  # 最后一层，第一个样本
        valid_mask = attention_mask[0].cpu().numpy().astype(bool)
        valid_final = final_hidden[valid_mask]

        if len(valid_final) > 1 and valid_final.shape[1] > 1:
            # 如果token数量太少，就不做t-SNE
            if len(valid_final) >= 3:
                tsne = TSNE(n_components=2, random_state=42, perplexity=min(5, len(valid_final) - 1))
                tsne_result = tsne.fit_transform(valid_final)

                # 为每个token分配颜色
                colors = plt.cm.tab10(np.linspace(0, 1, len(valid_final)))
                scatter = ax4.scatter(tsne_result[:, 0], tsne_result[:, 1], c=colors, s=100, alpha=0.7)

                # 添加token索引标签
                for i, (x, y) in enumerate(tsne_result):
                    ax4.annotate(f'T{i}', (x, y), xytext=(5, 5), textcoords='offset points', fontsize=8)
            else:
                ax4.text(0.5, 0.5, 'Token数量不足\n无法进行t-SNE', ha='center', va='center', transform=ax4.transAxes)
        else:
            ax4.text(0.5, 0.5, '数据不足\n无法可视化', ha='center', va='center', transform=ax4.transAxes)

        ax4.set_title('最终层表示聚类 (t-SNE)')
        ax4.set_xlabel('t-SNE维度1')
        ax4.set_ylabel('t-SNE维度2')
        ax4.grid(True, alpha=0.3)

        # 5. 梯度流分析（模拟）
        ax5 = axes[1, 1]

        # 计算各层表示的变化率作为梯度流的代理
        gradient_flows = []
        for i in range(1, len(all_hidden)):
            prev_hidden = all_hidden[i - 1].detach().cpu().numpy()
            curr_hidden = all_hidden[i].detach().cpu().numpy()

            # 计算变化率
            change_rate = np.mean(np.abs(curr_hidden - prev_hidden))
            gradient_flows.append(change_rate)

        x_flow_layers = range(1, len(all_hidden))
        bars = ax5.bar(x_flow_layers, gradient_flows, alpha=0.7, color='lightcoral')

        ax5.set_xlabel('层数')
        ax5.set_ylabel('表示变化率')
        ax5.set_title('梯度流分析（变化率）')
        ax5.set_xticks(x_flow_layers)
        ax5.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, flow in zip(bars, gradient_flows):
            height = bar.get_height()
            ax5.text(bar.get_x() + bar.get_width() / 2., height,
                     f'{flow:.4f}', ha='center', va='bottom', fontsize=8)

        # 6. 信息保持分析
        ax6 = axes[1, 2]

        # 计算各层相对于输入的信息保持程度
        input_hidden = all_hidden[0].detach().cpu().numpy()
        information_retention = []

        for hidden in all_hidden:
            hidden_cpu = hidden.detach().cpu().numpy()

            # 计算与输入的相关性
            input_flat = input_hidden.flatten()
            hidden_flat = hidden_cpu.flatten()

            correlation = np.corrcoef(input_flat, hidden_flat)[0, 1]
            information_retention.append(correlation if not np.isnan(correlation) else 0)

        ax6.plot(x_layers, information_retention, 'o-', linewidth=2, markersize=8, color='purple')
        ax6.set_xlabel('层数')
        ax6.set_ylabel('与输入的相关性')
        ax6.set_title('信息保持分析')
        ax6.grid(True, alpha=0.3)
        ax6.set_xticks(x_layers)
        ax6.axhline(y=0, color='red', linestyle='--', alpha=0.5)

        plt.tight_layout()
        plt.savefig(f'{self.save_path}/representation_learning.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"   💾 表示学习分析已保存: {self.save_path}/representation_learning.png")

        # 打印数值分析结果
        print(f"\n📊 表示学习数值分析:")
        print(f"   初始表示质量: {layer_qualities[0]:.4f}")
        print(f"   最终表示质量: {layer_qualities[-1]:.4f}")
        print(f"   质量提升: {layer_qualities[-1] - layer_qualities[0]:.4f}")
        print(f"   平均稀疏性: {np.mean(sparsities):.4f}")
        print(
            f"   最大梯度流: {np.max(gradient_flows) if gradient_flows else 0:.4f} (层{np.argmax(gradient_flows) + 1 if gradient_flows else 0})")
        print(f"   信息保持度: {information_retention[0]:.4f} → {information_retention[-1]:.4f}")

        # 分析学习模式
        if layer_qualities[-1] > layer_qualities[0]:
            learning_pattern = "渐进式学习"
        elif np.std(layer_qualities) < 0.1:
            learning_pattern = "稳定式学习"
        else:
            learning_pattern = "波动式学习"

        print(f"   学习模式: {learning_pattern}")

        return {
            'layer_qualities': layer_qualities,
            'activation_stats': activation_stats,
            'position_effects': position_effects,
            'gradient_flows': gradient_flows,
            'information_retention': information_retention
        }


def main():
    """主函数：运行编码器教程"""
    print("🎓 欢迎来到Transformer编码器深度学习教程！")
    print("本教程将带你深入理解编码器的工作原理")
    save_path = "./out"
    visualizer = EncoderVisualizer(save_path)

    try:
        # 第1步：位置编码
        visualizer.step1_positional_encoding()

        input("\n按回车键继续到第2步...")

        # 第2步：编码器层
        visualizer.step2_encoder_layer()

        input("\n按回车键继续到第3步...")

        # 第3步：完整编码器
        visualizer.step3_complete_encoder()

        input("\n按回车键继续到第4步...")

        # 第4步：编码器分析
        visualizer.step4_encoder_analysis()

        print("\n🎉 编码器教程完成！")
        print("📁 所有可视化图片已保存在 tutorial_scripts/ 目录下")
        print("📚 接下来可以运行: python tutorial_scripts/step3_training.py")

    except KeyboardInterrupt:
        print("\n\n👋 教程被用户中断，再见！")


if __name__ == "__main__":
    setup_chinese_font()
    setup_proxy()
    main()
