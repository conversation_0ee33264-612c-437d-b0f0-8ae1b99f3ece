#!/usr/bin/env python3
"""
第一步：深度理解注意力机制
详细演示多头注意力的工作原理，包含可视化和逐步计算
"""

import torch
import torch.nn.functional as F
import numpy as np
import sys
import os
from pathlib import Path

# 设置matplotlib后端为非交互式
import matplotlib
matplotlib.use('Agg')  # 使用非GUI后端
import matplotlib.pyplot as plt
import seaborn as sns

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))
from utils import setup_proxy
from src.transformer.attention import MultiHeadAttention

setup_proxy()

class AttentionVisualizer:
    """注意力机制可视化器"""
    
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🎯 注意力机制学习器初始化")
        print(f"   设备: {self.device}")
        
        # 设置matplotlib中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def step1_basic_attention(self):
        """第1步：理解基础注意力计算"""
        print("\n" + "="*60)
        print("📚 第1步：基础注意力机制原理")
        print("="*60)
        
        # 创建简单的示例
        seq_length = 5
        d_model = 8
        
        print(f"📊 创建示例数据:")
        print(f"   序列长度: {seq_length}")
        print(f"   模型维度: {d_model}")
        
        # 创建查询、键、值矩阵
        torch.manual_seed(42)  # 固定随机种子以便复现
        Q = torch.randn(1, seq_length, d_model)
        K = torch.randn(1, seq_length, d_model)
        V = torch.randn(1, seq_length, d_model)
        
        print(f"\n🔍 输入矩阵形状:")
        print(f"   Q (查询): {Q.shape}")
        print(f"   K (键):   {K.shape}")
        print(f"   V (值):   {V.shape}")
        
        # 手动计算注意力
        print(f"\n⚙️  手动计算注意力步骤:")
        
        # 步骤1: 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1))
        print(f"   1. 计算QK^T: {scores.shape}")
        scores_np = scores.squeeze(0).detach().numpy()
        print(f"      分数矩阵:")
        for i, row in enumerate(scores_np):
            print(f"        [{' '.join(f'{x:6.3f}' for x in row)}]")

        # 步骤2: 缩放
        d_k = d_model
        scaled_scores = scores / np.sqrt(d_k)
        print(f"\n   2. 缩放 (除以√d_k={np.sqrt(d_k):.2f}):")
        scaled_np = scaled_scores.squeeze(0).detach().numpy()
        print(f"      缩放后分数:")
        for i, row in enumerate(scaled_np):
            print(f"        [{' '.join(f'{x:6.3f}' for x in row)}]")

        # 步骤3: Softmax
        attention_weights = F.softmax(scaled_scores, dim=-1)
        print(f"\n   3. Softmax归一化:")
        weights_np = attention_weights.squeeze(0).detach().numpy()
        print(f"      注意力权重:")
        for i, row in enumerate(weights_np):
            print(f"        [{' '.join(f'{x:6.3f}' for x in row)}]")

        # 验证权重和为1
        weight_sums = attention_weights.sum(dim=-1)
        sums_np = weight_sums.squeeze(0).detach().numpy()
        print(f"      权重和验证: [{' '.join(f'{x:.3f}' for x in sums_np)}]")
        
        # 步骤4: 加权求和
        output = torch.matmul(attention_weights, V)
        print(f"\n   4. 加权求和得到输出: {output.shape}")
        
        # 可视化注意力权重
        self._visualize_attention_weights(
            attention_weights.squeeze(0).detach().numpy(),
            title="基础注意力权重矩阵",
            save_path="tutorial_scripts/attention_basic.png"
        )
        
        return Q, K, V, attention_weights, output
    
    def step2_multi_head_attention(self):
        """第2步：多头注意力机制"""
        print("\n" + "="*60)
        print("📚 第2步：多头注意力机制")
        print("="*60)
        
        # 参数设置
        batch_size = 1
        seq_length = 6
        d_model = 512
        n_heads = 8
        
        print(f"📊 多头注意力参数:")
        print(f"   序列长度: {seq_length}")
        print(f"   模型维度: {d_model}")
        print(f"   注意力头数: {n_heads}")
        print(f"   每头维度: {d_model // n_heads}")
        
        # 创建多头注意力层
        mha = MultiHeadAttention(d_model, n_heads, dropout=0.0)
        mha = mha.to(self.device)
        
        # 创建输入
        x = torch.randn(batch_size, seq_length, d_model).to(self.device)
        
        print(f"\n🔍 输入形状: {x.shape}")
        
        # 详细跟踪多头注意力计算过程
        print(f"\n⚙️  多头注意力计算过程:")
        
        with torch.no_grad():
            # 1. 线性变换得到Q、K、V
            Q = mha.w_q(x)
            K = mha.w_k(x)
            V = mha.w_v(x)
            
            print(f"   1. 线性变换:")
            print(f"      Q: {Q.shape}")
            print(f"      K: {K.shape}")
            print(f"      V: {V.shape}")
            
            # 2. 重塑为多头形式
            Q = Q.view(batch_size, seq_length, n_heads, d_model // n_heads).transpose(1, 2)
            K = K.view(batch_size, seq_length, n_heads, d_model // n_heads).transpose(1, 2)
            V = V.view(batch_size, seq_length, n_heads, d_model // n_heads).transpose(1, 2)
            
            print(f"\n   2. 重塑为多头:")
            print(f"      Q: {Q.shape} [batch, heads, seq_len, d_k]")
            print(f"      K: {K.shape}")
            print(f"      V: {V.shape}")
            
            # 3. 计算每个头的注意力
            scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(d_model // n_heads)
            attention_weights = F.softmax(scores, dim=-1)
            
            print(f"\n   3. 计算注意力:")
            print(f"      注意力分数: {scores.shape}")
            print(f"      注意力权重: {attention_weights.shape}")
            
            # 4. 应用注意力权重
            attention_output = torch.matmul(attention_weights, V)
            print(f"\n   4. 注意力输出: {attention_output.shape}")
            
            # 5. 拼接多头输出
            attention_output = attention_output.transpose(1, 2).contiguous().view(
                batch_size, seq_length, d_model
            )
            print(f"\n   5. 拼接多头: {attention_output.shape}")
            
            # 6. 输出投影
            final_output = mha.w_o(attention_output)
            print(f"\n   6. 输出投影: {final_output.shape}")
        
        # 使用官方接口验证
        official_output, official_weights = mha(x, x, x, return_attention=True)
        
        print(f"\n✅ 验证结果:")
        print(f"   手动计算输出: {final_output.shape}")
        print(f"   官方接口输出: {official_output.shape}")
        print(f"   输出差异: {torch.max(torch.abs(final_output - official_output)).item():.6f}")
        
        # 可视化多头注意力
        self._visualize_multi_head_attention(
            official_weights.squeeze(0).detach().cpu().numpy(),
            save_path="tutorial_scripts/attention_multihead.png"
        )
        
        return official_output, official_weights
    
    def step3_attention_patterns(self):
        """第3步：分析注意力模式"""
        print("\n" + "="*60)
        print("📚 第3步：注意力模式分析")
        print("="*60)
        
        # 创建有意义的输入序列
        sentences = [
            "The cat sat on the mat",
            "I love machine learning",
            "Attention is all you need"
        ]
        
        # 简单的词汇映射
        vocab = {}
        for sentence in sentences:
            for word in sentence.lower().split():
                if word not in vocab:
                    vocab[word] = len(vocab) + 1  # 0留给padding
        
        vocab['<pad>'] = 0
        print(f"📝 词汇表: {vocab}")
        
        # 转换句子为ID序列
        max_length = 8
        sequences = []
        for sentence in sentences:
            words = sentence.lower().split()
            ids = [vocab[word] for word in words]
            # 填充或截断到固定长度
            if len(ids) < max_length:
                ids.extend([0] * (max_length - len(ids)))
            else:
                ids = ids[:max_length]
            sequences.append(ids)
        
        print(f"\n📊 序列数据:")
        for i, (sentence, seq) in enumerate(zip(sentences, sequences)):
            print(f"   {i+1}. '{sentence}' -> {seq}")
        
        # 创建嵌入层和注意力层
        vocab_size = len(vocab)
        d_model = 128
        n_heads = 4
        
        embedding = torch.nn.Embedding(vocab_size, d_model, padding_idx=0)
        attention = MultiHeadAttention(d_model, n_heads, dropout=0.0)
        
        # 处理每个句子
        for i, (sentence, seq) in enumerate(zip(sentences, sequences)):
            print(f"\n🔍 分析句子 {i+1}: '{sentence}'")
            
            # 转换为张量并嵌入
            input_ids = torch.tensor([seq])
            embeddings = embedding(input_ids)
            
            # 计算注意力
            output, weights = attention(embeddings, embeddings, embeddings, return_attention=True)
            
            # 分析注意力模式
            avg_weights = weights.mean(dim=1).squeeze(0).detach().numpy()  # 平均所有头
            
            print(f"   注意力权重形状: {weights.shape}")
            print(f"   平均注意力权重:")
            
            # 显示词与词之间的注意力
            words = sentence.lower().split()
            if len(words) < max_length:
                words.extend(['<pad>'] * (max_length - len(words)))
            else:
                words = words[:max_length]
            
            # 找出最强的注意力连接
            for j, word in enumerate(words):
                if word != '<pad>':
                    max_attention_idx = np.argmax(avg_weights[j])
                    max_attention_score = avg_weights[j, max_attention_idx]
                    target_word = words[max_attention_idx]
                    print(f"     '{word}' 最关注 '{target_word}' (权重: {max_attention_score:.3f})")
            
            # 可视化这个句子的注意力
            self._visualize_sentence_attention(
                avg_weights, words, sentence,
                save_path=f"tutorial_scripts/attention_sentence_{i+1}.png"
            )
    
    def step4_masked_attention(self):
        """第4步：掩码注意力机制"""
        print("\n" + "="*60)
        print("📚 第4步：掩码注意力机制")
        print("="*60)
        
        seq_length = 6
        d_model = 64
        n_heads = 4
        
        # 创建注意力层
        attention = MultiHeadAttention(d_model, n_heads, dropout=0.0)
        
        # 创建输入
        x = torch.randn(1, seq_length, d_model)
        
        print(f"📊 掩码注意力演示:")
        print(f"   序列长度: {seq_length}")
        print(f"   输入形状: {x.shape}")
        
        # 1. 无掩码的注意力
        print(f"\n1️⃣ 无掩码注意力:")
        output_no_mask, weights_no_mask = attention(x, x, x, return_attention=True)
        avg_weights_no_mask = weights_no_mask.mean(dim=1).squeeze(0).detach().numpy()
        
        print(f"   注意力权重矩阵:")
        print(f"{avg_weights_no_mask}")
        
        # 2. 因果掩码（下三角掩码）
        print(f"\n2️⃣ 因果掩码注意力（用于解码器）:")
        causal_mask = torch.tril(torch.ones(seq_length, seq_length))
        causal_mask = causal_mask.unsqueeze(0)  # 添加batch维度
        
        print(f"   因果掩码:")
        print(f"{causal_mask.squeeze(0).numpy()}")
        
        output_causal, weights_causal = attention(x, x, x, mask=causal_mask, return_attention=True)
        avg_weights_causal = weights_causal.mean(dim=1).squeeze(0).detach().numpy()
        
        print(f"\n   掩码后注意力权重:")
        print(f"{avg_weights_causal}")
        
        # 3. 填充掩码
        print(f"\n3️⃣ 填充掩码注意力:")
        # 模拟序列中有padding
        padding_mask = torch.ones(1, seq_length, seq_length)
        padding_mask[:, :, 4:] = 0  # 假设位置4和5是padding
        
        print(f"   填充掩码 (位置4,5是padding):")
        print(f"{padding_mask.squeeze(0).numpy()}")
        
        output_padding, weights_padding = attention(x, x, x, mask=padding_mask, return_attention=True)
        avg_weights_padding = weights_padding.mean(dim=1).squeeze(0).detach().numpy()
        
        print(f"\n   掩码后注意力权重:")
        print(f"{avg_weights_padding}")
        
        # 可视化不同掩码的效果
        self._visualize_mask_comparison(
            avg_weights_no_mask, avg_weights_causal, avg_weights_padding,
            save_path="tutorial_scripts/attention_masks.png"
        )
    
    def _visualize_attention_weights(self, weights, title, save_path):
        """可视化注意力权重矩阵"""
        plt.figure(figsize=(8, 6))
        sns.heatmap(weights, annot=True, fmt='.3f', cmap='Blues', 
                   xticklabels=[f'K{i}' for i in range(weights.shape[1])],
                   yticklabels=[f'Q{i}' for i in range(weights.shape[0])])
        plt.title(title)
        plt.xlabel('Key Position')
        plt.ylabel('Query Position')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图形以释放内存
        print(f"   💾 注意力权重图已保存: {save_path}")
    
    def _visualize_multi_head_attention(self, weights, save_path):
        """可视化多头注意力"""
        n_heads = weights.shape[0]
        fig, axes = plt.subplots(2, 4, figsize=(16, 8))
        axes = axes.flatten()
        
        for i in range(n_heads):
            sns.heatmap(weights[i], annot=True, fmt='.2f', cmap='Blues', 
                       ax=axes[i], cbar=False)
            axes[i].set_title(f'Head {i+1}')
            axes[i].set_xlabel('Key')
            axes[i].set_ylabel('Query')
        
        plt.suptitle('多头注意力权重可视化')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"   💾 多头注意力图已保存: {save_path}")
    
    def _visualize_sentence_attention(self, weights, words, sentence, save_path):
        """可视化句子注意力"""
        # 只显示非padding的部分
        valid_length = len([w for w in words if w != '<pad>'])
        valid_weights = weights[:valid_length, :valid_length]
        valid_words = words[:valid_length]
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(valid_weights, annot=True, fmt='.3f', cmap='Reds',
                   xticklabels=valid_words, yticklabels=valid_words)
        plt.title(f'句子注意力模式: "{sentence}"')
        plt.xlabel('被关注的词')
        plt.ylabel('关注的词')
        plt.xticks(rotation=45)
        plt.yticks(rotation=0)
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"   💾 句子注意力图已保存: {save_path}")
    
    def _visualize_mask_comparison(self, no_mask, causal, padding, save_path):
        """比较不同掩码的效果"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 无掩码
        sns.heatmap(no_mask, annot=True, fmt='.2f', cmap='Blues', ax=axes[0])
        axes[0].set_title('无掩码')
        axes[0].set_xlabel('Key Position')
        axes[0].set_ylabel('Query Position')
        
        # 因果掩码
        sns.heatmap(causal, annot=True, fmt='.2f', cmap='Greens', ax=axes[1])
        axes[1].set_title('因果掩码')
        axes[1].set_xlabel('Key Position')
        axes[1].set_ylabel('Query Position')
        
        # 填充掩码
        sns.heatmap(padding, annot=True, fmt='.2f', cmap='Oranges', ax=axes[2])
        axes[2].set_title('填充掩码')
        axes[2].set_xlabel('Key Position')
        axes[2].set_ylabel('Query Position')
        
        plt.suptitle('不同掩码类型的注意力模式比较')
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"   💾 掩码比较图已保存: {save_path}")


def main():
    """主函数：运行注意力机制教程"""
    print("🎓 欢迎来到Transformer注意力机制深度学习教程！")
    print("本教程将带你深入理解注意力机制的工作原理")
    
    visualizer = AttentionVisualizer()
    
    try:
        # 第1步：基础注意力
        visualizer.step1_basic_attention()
        
        input("\n按回车键继续到第2步...")
        
        # 第2步：多头注意力
        visualizer.step2_multi_head_attention()
        
        input("\n按回车键继续到第3步...")
        
        # 第3步：注意力模式分析
        visualizer.step3_attention_patterns()
        
        input("\n按回车键继续到第4步...")
        
        # 第4步：掩码注意力
        visualizer.step4_masked_attention()
        
        print("\n🎉 注意力机制教程完成！")
        print("📁 所有可视化图片已保存在 tutorial_scripts/ 目录下")
        print("📚 接下来可以运行: python tutorial_scripts/step2_encoder.py")
        
    except KeyboardInterrupt:
        print("\n\n👋 教程被用户中断，再见！")


if __name__ == "__main__":
    main()
