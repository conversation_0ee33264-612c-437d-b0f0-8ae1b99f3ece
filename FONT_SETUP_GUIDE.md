# 🎨 中文字体设置指南

## 📋 概述

本项目已将所有中文字体设置功能统一到 `utils.py` 中，提供了三个主要函数来处理matplotlib中文字体显示问题。

## 🔧 可用函数

### 1. `setup_chinese_font()` - 智能字体设置

**功能**：自动检测系统中可用的中文字体，按优先级选择最佳字体。

**特点**：
- 🧠 智能检测系统字体
- 📊 详细的设置反馈
- 🔄 跨平台兼容
- ⚠️ 完整的错误处理

**使用方法**：
```python
from utils import setup_chinese_font
import matplotlib.pyplot as plt

# 设置中文字体
selected_font = setup_chinese_font()
print(f"选择的字体: {selected_font}")

# 现在可以正常显示中文
plt.title('中文标题')
plt.xlabel('横坐标')
plt.ylabel('纵坐标')
```

### 2. `setup_chinese_font_simple()` - 简化字体设置

**功能**：直接使用最常见的字体设置，适用于大多数情况。

**特点**：
- ⚡ 快速简单
- 🎯 教程专用
- 📦 轻量级
- ✅ 高兼容性

**使用方法**：
```python
from utils import setup_chinese_font_simple
import matplotlib.pyplot as plt

# 简化字体设置
setup_chinese_font_simple()

# 直接使用中文
plt.title('中文标题')
```

### 3. `check_chinese_font_support()` - 字体支持检查

**功能**：检查系统中文字体支持情况，返回详细信息。

**特点**：
- 🔍 全面检测
- 📊 详细统计
- 📝 支持报告
- 🛠️ 调试工具

**使用方法**：
```python
from utils import check_chinese_font_support

# 检查字体支持
result = check_chinese_font_support()

print(f"系统总字体数: {result['total_fonts']}")
print(f"可用中文字体数: {result['chinese_fonts_available']}")

for font_name, info in result['supported_fonts'].items():
    status = "✅" if info['available'] else "❌"
    print(f"{status} {font_name} - {info['description']}")
```

## 🎯 支持的字体

按优先级排序：

| 字体名称 | 描述 | 平台 | 优先级 |
|---------|------|------|--------|
| SimHei | Windows 黑体 | Windows | 1 |
| Microsoft YaHei | Windows 微软雅黑 | Windows | 2 |
| WenQuanYi Micro Hei | 文泉驿微米黑 | Linux | 3 |
| Noto Sans CJK SC | Google Noto 字体 | 跨平台 | 4 |
| Source Han Sans CN | Adobe 思源黑体 | 跨平台 | 5 |
| AR PL UMing CN | 文鼎PL细上海宋 | Linux | 6 |
| Droid Sans Fallback | Android 备选字体 | Android | 7 |
| DejaVu Sans | 备选英文字体 | 跨平台 | 8 |

## 📚 在项目中的使用

### 教程脚本

所有教程脚本已更新为使用统一的字体设置：

```python
# tutorial_scripts/step1_attention.py
# tutorial_scripts/step2_encoder.py  
# tutorial_scripts/step3_training.py
# tutorial_scripts/visualize.py

from utils import setup_chinese_font_simple

class SomeVisualizer:
    def __init__(self):
        # 设置中文字体
        setup_chinese_font_simple()
```

### 新项目中使用

1. **复制 `utils.py` 中的字体函数**到你的新项目
2. **在绘图前调用**字体设置函数
3. **选择合适的函数**：
   - 简单项目：使用 `setup_chinese_font_simple()`
   - 复杂项目：使用 `setup_chinese_font()`
   - 调试问题：使用 `check_chinese_font_support()`

## 🛠️ 测试工具

### 字体测试工具

```bash
python font_test_tool.py
```

**功能菜单**：
1. 检查系统字体支持情况
2. 测试智能字体设置
3. 测试简化字体设置  
4. 生成中文字体测试图表
5. 退出

### 快速测试

```bash
python test_chinese_font.py
```

生成综合测试图表，验证中文显示效果。

## 🔧 故障排除

### 常见问题

1. **中文显示为方块**
   ```python
   # 解决方案：检查字体支持
   from utils import check_chinese_font_support
   result = check_chinese_font_support()
   print(f"可用中文字体数: {result['chinese_fonts_available']}")
   ```

2. **字体警告信息**
   ```python
   # 解决方案：字体设置函数已包含警告抑制
   from utils import setup_chinese_font_simple
   setup_chinese_font_simple()  # 自动抑制警告
   ```

3. **特殊字符显示问题**
   ```python
   # 解决方案：避免使用特殊Unicode字符
   # 使用普通字符替代：• → - ， − → -
   ```

### 系统特定解决方案

**Windows**：
- 系统自带 SimHei 和 Microsoft YaHei
- 通常无需额外安装

**Linux**：
```bash
# 安装中文字体
sudo apt install fonts-wqy-microhei
sudo apt install fonts-noto-cjk
```

**macOS**：
- 系统自带中文字体
- 可能需要手动指定字体名称

## 📈 最佳实践

### 1. 项目初始化时设置

```python
# 在项目入口文件中
from utils import setup_chinese_font_simple

# 全局设置一次
setup_chinese_font_simple()
```

### 2. 类初始化时设置

```python
class Visualizer:
    def __init__(self):
        # 在可视化类中设置
        setup_chinese_font_simple()
```

### 3. 函数级别设置

```python
def create_plot():
    # 在绘图函数中设置
    setup_chinese_font_simple()
    plt.title('中文标题')
```

## 🚀 扩展到其他项目

### 复制字体函数

1. **复制函数**：从 `utils.py` 复制字体相关函数
2. **调整导入**：根据新项目结构调整导入路径
3. **测试验证**：使用测试工具验证字体设置

### 示例：新项目集成

```python
# new_project/utils.py
def setup_chinese_font_simple():
    """简化版中文字体设置"""
    try:
        import matplotlib.pyplot as plt
        import warnings
        
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
        
        return True
    except Exception as e:
        print(f"字体设置失败: {e}")
        return False

# new_project/main.py
from utils import setup_chinese_font_simple
import matplotlib.pyplot as plt

setup_chinese_font_simple()
plt.title('新项目中文标题')
plt.show()
```

## 📞 技术支持

如果遇到字体问题：

1. **运行检测工具**：`python font_test_tool.py`
2. **查看系统字体**：选择菜单选项1
3. **测试字体设置**：选择菜单选项3
4. **生成测试图表**：选择菜单选项4

---

**🎨 现在你可以在任何项目中轻松使用中文字体了！**
