#!/usr/bin/env python3
"""
主训练脚本
整合所有组件，训练Transformer模型

支持的任务：
1. 翻译任务 - 使用完整的Transformer模型
2. 分类任务 - 使用编码器 + 分类头

使用方法：
python train.py --config config.yaml --task translation
python train.py --config config.yaml --task classification
"""

import argparse
import yaml
import torch
import torch.nn as nn
from pathlib import Path
import sys

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from src.transformer.transformer import Transformer
from src.transformer.encoder import PooledTransformerEncoder
from src.data.dataset import create_dataloaders
from src.training.trainer import Trainer

# 设置代理
from src.utils.utils import setup_proxy, setup_chinese_font


class ClassificationModel(nn.Module):
    """
    基于Transformer编码器的分类模型
    """

    def __init__(self, config: dict, num_classes: int = 2):
        """
        初始化分类模型
        
        Args:
            config: 配置字典
            num_classes: 分类数量
        """
        super(ClassificationModel, self).__init__()

        # 使用带池化的编码器
        self.encoder = PooledTransformerEncoder(
            vocab_size=config['model']['vocab_size'],
            d_model=config['model']['d_model'],
            n_heads=config['model']['n_heads'],
            n_layers=config['model']['n_layers'],
            d_ff=config['model']['d_ff'],
            max_length=config['model']['max_seq_length'],
            dropout=config['model']['dropout'],
            pooling_strategy="mean",
            pad_token_id=0
        )

        # 分类头
        self.classifier = nn.Sequential(
            nn.Dropout(config['model']['dropout']),
            nn.Linear(config['model']['d_model'], config['model']['d_model'] // 2),
            nn.ReLU(),
            nn.Dropout(config['model']['dropout']),
            nn.Linear(config['model']['d_model'] // 2, num_classes)
        )

    def forward(self, input_ids, attention_mask=None):
        """前向传播"""
        # 获取编码器输出
        sequence_output, pooled_output = self.encoder(input_ids, attention_mask)

        # 分类
        logits = self.classifier(pooled_output)

        return logits


def load_config(config_path: str) -> dict:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f) # 使用yaml模块加载配置文件
    return config


def create_model(config: dict, task_type: str, dataset=None) -> nn.Module:
    """
    创建模型
    
    Args:
        config: 配置字典
        task_type: 任务类型
        dataset: 数据集（用于获取词汇表大小）
        
    Returns:
        model: 创建的模型
    """
    if task_type == "classification":
        # 分类任务使用编码器
        model = ClassificationModel(config, num_classes=2)  # IMDB是二分类

    elif task_type == "translation":
        # 翻译任务使用完整的Transformer
        if dataset is None:
            raise ValueError("翻译任务需要提供数据集以获取词汇表大小")

        # 从数据集获取词汇表大小
        src_vocab_size = len(dataset.src_vocab)
        tgt_vocab_size = len(dataset.tgt_vocab)

        model = Transformer(
            src_vocab_size=src_vocab_size,
            tgt_vocab_size=tgt_vocab_size,
            d_model=config['model']['d_model'],
            n_heads=config['model']['n_heads'],
            n_encoder_layers=config['model']['n_layers'],
            n_decoder_layers=config['model']['n_layers'],
            d_ff=config['model']['d_ff'],
            max_length=config['model']['max_seq_length'],
            dropout=config['model']['dropout'],
            pad_token_id=0
        )

    else:
        raise ValueError(f"不支持的任务类型: {task_type}")

    return model


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="训练Transformer模型") # 创建参数解析器
    parser.add_argument("--config", type=str, default="config.yaml", help="配置文件路径")
    parser.add_argument("--task", type=str, choices=["classification", "translation"], default="translation", help="任务类型")
    parser.add_argument("--resume", type=str, help="恢复训练的检查点路径")
    parser.add_argument("--subset_size", type=int, help="数据集子集大小（用于快速测试）")

    args = parser.parse_args() # 解析命令行参数

    # 加载配置
    print(f"📋 加载配置文件: {args.config}")
    config = load_config(args.config)

    # 设置设备
    if config['device']['use_cuda'] and torch.cuda.is_available():
        device = torch.device(f"cuda:{config['device']['device_id']}")
        print(f"🚀 使用GPU: {torch.cuda.get_device_name(device)}")

        # 启用混合精度训练
        if config['device']['mixed_precision']:
            print("⚡ 启用混合精度训练")
    else:
        device = torch.device("cpu")
        print("💻 使用CPU")

    # 创建数据加载器
    print(f"📊 准备数据集...")
    if args.task == "classification":
        train_loader, val_loader = create_dataloaders(
            dataset_type="imdb",
            batch_size=config['training']['batch_size'],
            max_length=config['data']['max_length'],
            subset_size=args.subset_size
        )
        dataset = None

    elif args.task == "translation":
        train_loader, val_loader = create_dataloaders(
            dataset_type="translation",
            batch_size=config['training']['batch_size'],
            max_length=config['data']['max_length'],
            subset_size=args.subset_size
        )
        # 获取数据集以获取词汇表信息
        dataset = train_loader.dataset

    print(f"   训练批次数: {len(train_loader)}")
    print(f"   验证批次数: {len(val_loader)}")

    # 创建模型
    print(f"🏗️  创建模型...")
    model = create_model(config, args.task, dataset)
    print(f"   模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 创建训练器
    print(f"🎯 创建训练器...")
    trainer = Trainer(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        config=config,
        device=device,
        task_type=args.task
    )

    # 恢复训练（如果指定）
    if args.resume:
        print(f"🔄 恢复训练: {args.resume}")
        trainer.load_checkpoint(args.resume)

    # 开始训练
    try:
        trainer.train()
    except KeyboardInterrupt:
        print("\n⏹️  训练被用户中断")
        trainer.save_checkpoint("interrupted_checkpoint.pt")
        print("💾 已保存中断检查点")
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        trainer.save_checkpoint("error_checkpoint.pt")
        print("💾 已保存错误检查点")
        raise

    print("🎉 训练完成!")


if __name__ == "__main__":
    setup_chinese_font() # 设置中文字体
    setup_proxy() # 设置代理

    main()
