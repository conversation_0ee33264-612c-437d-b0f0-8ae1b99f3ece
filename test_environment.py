#!/usr/bin/env python3
"""
环境测试脚本
验证所有依赖包是否正确安装，GPU是否可用
"""

import sys
import torch
import numpy as np
import yaml
from pathlib import Path

def test_imports():
    """测试所有必要的包是否能正确导入"""
    print("🔍 测试包导入...")
    
    try:
        import torch
        import torchvision
        import torchaudio
        import numpy as np
        import pandas as pd
        import matplotlib.pyplot as plt
        import seaborn as sns
        import sklearn
        import datasets
        import transformers
        import tokenizers
        import tqdm
        import yaml
        print("✅ 所有包导入成功!")
        return True
    except ImportError as e:
        print(f"❌ 包导入失败: {e}")
        return False

def test_cuda():
    """测试CUDA是否可用"""
    print("\n🔍 测试CUDA...")
    
    if torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        current_device = torch.cuda.current_device()
        device_name = torch.cuda.get_device_name(current_device)
        memory_total = torch.cuda.get_device_properties(current_device).total_memory / 1024**3
        
        print(f"✅ CUDA可用!")
        print(f"   设备数量: {device_count}")
        print(f"   当前设备: {current_device}")
        print(f"   设备名称: {device_name}")
        print(f"   显存总量: {memory_total:.1f} GB")
        
        # 测试简单的GPU计算
        try:
            x = torch.randn(1000, 1000).cuda()
            y = torch.randn(1000, 1000).cuda()
            z = torch.mm(x, y) # 矩阵乘法
            print("   GPU计算测试通过!")
            return True
        except Exception as e:
            print(f"   ❌ GPU计算测试失败: {e}")
            return False
    else:
        print("❌ CUDA不可用，将使用CPU")
        return False

def test_config():
    """测试配置文件是否能正确加载"""
    print("\n🔍 测试配置文件...")
    
    try:
        config_path = Path("config.yaml")
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            print("✅ 配置文件加载成功!")
            print(f"   模型维度: {config['model']['d_model']}")
            print(f"   注意力头数: {config['model']['n_heads']}")
            print(f"   层数: {config['model']['n_layers']}")
            print(f"   批次大小: {config['training']['batch_size']}")
            return True
        else:
            print("❌ 配置文件不存在")
            return False
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False

def test_memory_usage():
    """测试内存使用情况"""
    print("\n🔍 测试内存使用...")
    
    try:
        # 创建一个小型张量来测试内存
        if torch.cuda.is_available():
            device = torch.device('cuda')
            # 测试创建一个模拟的小型transformer输入
            batch_size = 16
            seq_length = 512
            d_model = 512
            
            # 模拟输入张量
            input_ids = torch.randint(0, 30000, (batch_size, seq_length)).to(device)
            embeddings = torch.randn(batch_size, seq_length, d_model).to(device)
            
            # 检查显存使用
            memory_allocated = torch.cuda.memory_allocated() / 1024**3
            memory_reserved = torch.cuda.memory_reserved() / 1024**3
            
            print(f"✅ 内存测试通过!")
            print(f"   已分配显存: {memory_allocated:.2f} GB")
            print(f"   已保留显存: {memory_reserved:.2f} GB")
            
            # 清理内存
            del input_ids, embeddings
            torch.cuda.empty_cache()
            return True
        else:
            print("   使用CPU模式")
            return True
    except Exception as e:
        print(f"❌ 内存测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始环境测试...\n")
    
    tests = [
        test_imports,
        test_cuda,
        test_config,
        test_memory_usage
    ]
    
    results = []
    for test in tests:
        results.append(test()) # 运行测试
    
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print(f"   通过: {sum(results)}/{len(results)}")
    
    if all(results): # 所有测试通过,ALL 用于检测所有测试结果是否都为True
        print("🎉 所有测试通过! 环境配置成功!")
    else:
        print("⚠️  部分测试失败，请检查环境配置")
    
    print("="*50)

if __name__ == "__main__":
    main()
