#!/usr/bin/env python3
"""
快速中文字体测试
验证matplotlib后端兼容性修复
"""

import matplotlib
# 设置后端以避免PyCharm兼容性问题
matplotlib.use('TkAgg')

import matplotlib.pyplot as plt
import numpy as np
from utils import setup_chinese_font_simple

def quick_test():
    """快速测试中文字体显示"""
    print("🚀 快速中文字体测试")
    print("="*40)
    
    # 设置中文字体
    setup_chinese_font_simple()
    
    # 创建简单测试图
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 测试数据
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # 绘制图形
    ax.plot(x, y1, 'b-', label='正弦波', linewidth=2)
    ax.plot(x, y2, 'r--', label='余弦波', linewidth=2)
    
    # 设置中文标签
    ax.set_title('中文字体测试 - 三角函数图像', fontsize=16, fontweight='bold')
    ax.set_xlabel('横坐标 (弧度)', fontsize=12)
    ax.set_ylabel('纵坐标 (幅值)', fontsize=12)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 添加文本说明
    ax.text(0.02, 0.98, '这是中文字体测试\n如果你能看到这些中文字符\n说明字体设置成功！', 
            transform=ax.transAxes, fontsize=12, verticalalignment='top',
            bbox=dict(boxstyle="round,pad=0.5", facecolor='lightyellow', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图片
    plt.savefig('quick_font_test.png', dpi=150, bbox_inches='tight')
    print("📁 测试图片已保存: quick_font_test.png")
    
    # 显示图形
    try:
        plt.show()
        print("✅ 图形显示成功！")
    except Exception as e:
        print(f"⚠️  图形显示出现问题: {e}")
        print("但图片已保存，可以查看 quick_font_test.png")
    
    plt.close()

def test_backend_info():
    """测试后端信息"""
    print("\n🔧 后端信息:")
    print(f"   当前后端: {matplotlib.get_backend()}")

    # 测试字体信息
    import matplotlib.font_manager as fm
    fonts = [f.name for f in fm.fontManager.ttflist]
    chinese_fonts = [f for f in fonts if any(name in f for name in ['SimHei', 'Microsoft', 'WenQuanYi', 'Noto'])]

    print(f"\n🎨 字体信息:")
    print(f"   系统总字体数: {len(fonts)}")
    print(f"   检测到的中文字体: {len(chinese_fonts)}")
    if chinese_fonts:
        print(f"   中文字体列表: {chinese_fonts[:3]}...")  # 只显示前3个

def main():
    """主函数"""
    print("🧪 快速中文字体兼容性测试")
    print("="*50)
    print("这个测试用于验证matplotlib后端兼容性修复")
    print()
    
    try:
        # 测试后端信息
        test_backend_info()
        
        # 快速字体测试
        quick_test()
        
        print("\n🎉 测试完成！")
        print("如果没有出现错误，说明后端兼容性问题已修复")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
        print("\n🔧 可能的解决方案:")
        print("1. 确保安装了tkinter: sudo apt install python3-tk")
        print("2. 尝试其他后端: matplotlib.use('Qt5Agg') 或 matplotlib.use('Agg')")
        print("3. 在非GUI环境中使用: matplotlib.use('Agg')")

if __name__ == "__main__":
    main()
