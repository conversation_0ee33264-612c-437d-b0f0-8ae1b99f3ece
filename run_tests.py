#!/usr/bin/env python3
"""
测试运行脚本
运行所有单元测试和集成测试
"""

import unittest
import sys
from pathlib import Path


def run_all_tests():
    """运行所有测试"""
    print("🧪 开始运行所有测试...")
    print("=" * 50)
    
    # 发现并运行测试
    test_dir = Path(__file__).parent / "tests"
    loader = unittest.TestLoader()
    suite = loader.discover(str(test_dir), pattern='test_*.py')
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print(f"   运行测试数: {result.testsRun}")
    print(f"   失败: {len(result.failures)}")
    print(f"   错误: {len(result.errors)}")
    print(f"   跳过: {len(result.skipped)}")
    
    if result.wasSuccessful():
        print("🎉 所有测试通过!")
        return True
    else:
        print("❌ 部分测试失败!")
        
        # 显示失败详情
        if result.failures:
            print("\n失败的测试:")
            for test, traceback in result.failures:
                print(f"  - {test}: {traceback}")
        
        if result.errors:
            print("\n错误的测试:")
            for test, traceback in result.errors:
                print(f"  - {test}: {traceback}")
        
        return False


def run_specific_test(test_name: str):
    """运行特定测试"""
    print(f"🧪 运行测试: {test_name}")
    
    # 导入并运行特定测试
    test_module = f"tests.{test_name}"
    suite = unittest.TestLoader().loadTestsFromName(test_module)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    from src.utils.utils import setup_proxy
    # 设置代理
    setup_proxy()

    if len(sys.argv) > 1:
        # 运行特定测试
        test_name = sys.argv[1]
        success = run_specific_test(test_name)
    else:
        # 运行所有测试
        success = run_all_tests()
    
    sys.exit(0 if success else 1)
