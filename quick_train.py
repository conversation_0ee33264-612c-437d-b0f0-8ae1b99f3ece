#!/usr/bin/env python3
"""
快速训练脚本
用于演示Transformer训练流程

这个脚本使用小数据集和少量epoch进行快速训练，
主要用于验证整个训练流程的正确性。
"""

import yaml
import sys
from pathlib import Path



# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from train import main as train_main


def create_quick_config():
    """创建快速训练配置"""
    config = {
        'model': {
            'vocab_size': 30000,
            'd_model': 256,  # 减小模型尺寸
            'n_heads': 8,
            'n_layers': 4,   # 减少层数
            'd_ff': 1024,    # 减小FFN尺寸
            'max_seq_length': 128,  # 减小序列长度
            'dropout': 0.1,
            'max_position_embeddings': 128
        },
        'training': {
            'batch_size': 8,  # 减小批次大小
            'learning_rate': 0.0001,
            'num_epochs': 3,  # 只训练3个epoch
            'warmup_steps': 100,  # 减少预热步数
            'gradient_clip': 1.0,
            'save_every': 50,  # 更频繁地保存
            'optimizer': 'adam',
            'beta1': 0.9,
            'beta2': 0.98,
            'eps': 1e-9,
            'weight_decay': 0.01,
            'use_warmup': True
        },
        'data': {
            'dataset_name': 'imdb',
            'max_length': 128,
            'train_split': 0.8,
            'val_split': 0.1,
            'test_split': 0.1
        },
        'device': {
            'use_cuda': True,
            'device_id': 0,
            'mixed_precision': True
        },
        'logging': {
            'log_dir': './logs',
            'model_save_dir': './models',
            'tensorboard': True,
            'print_every': 10
        }
    }
    
    return config


def main():
    """主函数"""
    print("🚀 快速训练演示")
    print("=" * 50)
    
    # 创建配置
    config = create_quick_config()
    
    # 保存配置文件
    config_path = "quick_config.yaml"
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"📋 配置文件已保存: {config_path}")
    
    # 创建必要的目录
    Path("logs").mkdir(exist_ok=True)
    Path("models").mkdir(exist_ok=True)
    
    print("\n🎯 开始快速训练...")
    print("配置参数:")
    print(f"  模型维度: {config['model']['d_model']}")
    print(f"  层数: {config['model']['n_layers']}")
    print(f"  批次大小: {config['training']['batch_size']}")
    print(f"  训练轮数: {config['training']['num_epochs']}")
    print(f"  序列长度: {config['model']['max_seq_length']}")
    
    # 设置命令行参数
    sys.argv = [
        'train.py',
        '--config', config_path,
        '--task', 'translation',
        '--subset_size', '500'  # 使用小数据集
    ]
    
    try:
        # 运行训练
        train_main()
        
        print("\n🎉 快速训练完成!")
        print("📁 检查以下文件:")
        print("  - ./models/best_model.pt (最佳模型)")
        print("  - ./logs/tensorboard/ (TensorBoard日志)")
        
        # 检查模型文件是否存在
        model_path = Path("models/best_model.pt")
        if model_path.exists():
            print(f"✅ 模型文件已生成: {model_path}")
            print(f"   文件大小: {model_path.stat().st_size / 1024 / 1024:.1f} MB")
            
            # 显示如何使用演示脚本
            print(f"\n🎭 运行演示:")
            print(f"python demo.py --config {config_path} --model models/best_model.pt --task translation")
        else:
            print("⚠️  模型文件未找到，训练可能未完成")
    
    except KeyboardInterrupt:
        print("\n⏹️  训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练失败: {e}")
        print("请检查错误信息并重试")


if __name__ == "__main__":
    # 设置代理
    from src.utils.utils import setup_proxy
    setup_proxy()
    main()
