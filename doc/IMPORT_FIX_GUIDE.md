# 🔧 Python导入错误修复指南

## 📋 问题描述

当直接运行包含相对导入的Python脚本时，会出现以下错误：

```
ImportError: attempted relative import with no known parent package
```

## 🔍 错误原因

Python的相对导入（如 `from ..utils import something`）只能在包（package）内部使用。当你直接运行一个脚本时，Python不知道它的包结构，因此无法解析相对导入。

## ✅ 解决方案

### 方案1：修改为绝对导入（推荐）

**修改前**：
```python
# 错误的相对导入
sys.path.append(str(Path(__file__).parent))
from ..utils.utils import setup_chinese_font
from ..transformer.attention import MultiHeadAttention
```

**修改后**：
```python
# 正确的绝对导入
sys.path.append(str(Path(__file__).parent.parent.parent))  # 添加项目根目录
from src.utils.utils import setup_chinese_font
from src.transformer.attention import MultiHeadAttention
```

### 方案2：使用模块方式运行

不直接运行脚本，而是作为模块运行：

```bash
# 在项目根目录下运行
python -m src.temp.test_visualization

# 而不是
python src/temp/test_visualization.py
```

### 方案3：动态导入

```python
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 现在可以使用绝对导入
from src.utils.utils import setup_chinese_font
```

## 🎯 项目中的修复示例

### 修复 `src/temp/test_visualization.py`

**问题**：
```python
from ..utils.utils import setup_chinese_font  # ❌ 相对导入错误
```

**解决**：
```python
sys.path.append(str(Path(__file__).parent.parent.parent))  # 添加项目根目录
from src.utils.utils import setup_chinese_font  # ✅ 绝对导入正确
```

## 📁 路径层级说明

```
myTransfromer/                    # 项目根目录
├── src/                         # 源代码目录
│   ├── temp/                    # 临时测试目录
│   │   └── test_visualization.py # 当前文件位置
│   ├── utils/
│   │   └── utils.py             # 目标导入文件
│   └── transformer/
│       └── attention.py         # 目标导入文件
└── ...
```

**路径计算**：
- 当前文件：`src/temp/test_visualization.py`
- 需要到达：项目根目录 `myTransfromer/`
- 路径关系：`Path(__file__).parent.parent.parent`
  - `.parent` → `src/temp/`
  - `.parent.parent` → `src/`
  - `.parent.parent.parent` → `myTransfromer/`

## 🛠️ 通用修复模板

对于任何在 `src/` 子目录中的脚本，使用以下模板：

```python
#!/usr/bin/env python3
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 现在可以使用绝对导入
from src.utils.utils import setup_chinese_font, setup_proxy
from src.transformer.attention import MultiHeadAttention
# ... 其他导入

def main():
    # 你的代码
    pass

if __name__ == "__main__":
    main()
```

## 🔍 调试技巧

### 检查路径是否正确

```python
from pathlib import Path
import sys

# 打印当前文件路径
print(f"当前文件: {__file__}")
print(f"父目录: {Path(__file__).parent}")
print(f"项目根目录: {Path(__file__).parent.parent.parent}")

# 检查sys.path
print(f"Python路径: {sys.path}")
```

### 验证导入是否成功

```python
try:
    from src.utils.utils import setup_chinese_font
    print("✅ 导入成功")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
```

## 📚 最佳实践

1. **优先使用绝对导入**：更清晰，不依赖文件位置
2. **统一路径管理**：在项目中使用统一的路径添加方式
3. **避免深层嵌套**：减少复杂的相对路径关系
4. **使用 `__init__.py`**：让目录成为真正的Python包

## 🚀 验证修复

修复后，运行以下命令验证：

```bash
# 测试修复后的脚本
python src/temp/test_visualization.py

# 应该看到正常输出，而不是ImportError
```

## 📝 常见错误

### 错误1：路径层级计算错误
```python
# 错误：层级不够
sys.path.append(str(Path(__file__).parent))  # 只到了 src/temp/

# 正确：到达项目根目录
sys.path.append(str(Path(__file__).parent.parent.parent))  # 到达 myTransfromer/
```

### 错误2：导入路径错误
```python
# 错误：路径不完整
from utils.utils import setup_chinese_font

# 正确：完整的包路径
from src.utils.utils import setup_chinese_font
```

### 错误3：混用相对和绝对导入
```python
# 错误：混用导入方式
from ..utils.utils import setup_chinese_font  # 相对导入
from src.transformer.attention import MultiHeadAttention  # 绝对导入

# 正确：统一使用绝对导入
from src.utils.utils import setup_chinese_font
from src.transformer.attention import MultiHeadAttention
```

---

**✅ 现在你的导入问题已经修复！可以正常运行所有脚本了。**
